import joblib


def predict_category(features, model_file='model.pkl'):
    # 加载模型
    model = joblib.load(model_file)

    # 假设features是一个包含三个元素的列表或数组
    # 我们需要将其转换为与训练数据相同的格式（标准化）
    from sklearn.preprocessing import StandardScaler
    scaler = joblib.load('scaler.joblib')  # 假设我们在准备模块中也保存了scaler
    features_scaled = scaler.transform([features])

    # 预测
    prediction = model.predict(features_scaled)
    # 由于我们只关心是否为C类，所以直接返回0或1
    return prediction[0]


# 示例使用
if __name__ == '__main__':
    features = [40.56317992,46.08266102,0.248522284]  # 示例特征
    print(predict_category(features))  # 输出0或1