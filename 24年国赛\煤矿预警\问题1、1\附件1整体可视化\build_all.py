import pandas as pd
import matplotlib.pyplot as plt

# 假设Excel文件名为'sound_data.xlsx'，并且文件位于与Python脚本相同的目录中
file_path = '../../附件1 (Attachment 1).xlsx'

# 读取Excel文件
df = pd.read_excel(file_path, sheet_name='EMR')
print(df.columns)


# 将时间列转换为datetime类型，以便于绘图
df['时间 (time)'] = pd.to_datetime(df['时间 (time)'])

# 绘制散点图
plt.figure(figsize=(10, 6))
for class_name in df['类别 (class)'].unique():
    subset = df[df['类别 (class)'] == class_name]
    plt.scatter(subset['时间 (time)'], subset['电磁辐射 (EMR)'], label=class_name, s=3)

# 设置图表标题和标签
plt.title('EMR graph')
plt.xlabel('time')
plt.ylabel('EMR')
plt.legend()

# 显示图表
plt.show()
