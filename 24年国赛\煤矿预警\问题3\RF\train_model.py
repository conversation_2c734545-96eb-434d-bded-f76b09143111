import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from datetime import datetime
import joblib

# 加载训练好的随机森林分类器
model_file_path = 'trained_random_forest_model.pkl'
rf_classifier = joblib.load(model_file_path)

# 从"attachment3.xlsx"加载新数据
attachment3_file_path = 'attachment3.xlsx'
attachment3_df = pd.read_excel(attachment3_file_path)
attachment3_df['时间'] = pd.to_datetime(attachment3_df['时间 (time)'])

# 特征工程：添加滞后特征
# 假设我们添加过去1天和过去7天的电磁辐射作为特征
attachment3_df['EMR_lag1'] = attachment3_df['电磁辐射 (EMR)'].shift(1)
attachment3_df['EMR_lag7'] = attachment3_df['电磁辐射 (EMR)'].shift(7)

# 处理NaN值（由于滞后特征产生）
attachment3_df.fillna(0, inplace=True)

# 更新特征列表
features = ['电磁辐射 (EMR)', 'EMR_lag1', 'EMR_lag7']

# 选择最后一条记录进行预测
last_record = attachment3_df.iloc[-1][features].values.reshape(1, -1)

# 对最后一条记录进行类别概率预测
last_record_pred_probs = rf_classifier.predict_proba(last_record)

# 获取类别'B'的概率（前兆特征，编码后的索引为1）
probability_of_class_B = last_record_pred_probs[0][1]
print(f'类别B的概率（前兆特征）: {probability_of_class_B}')