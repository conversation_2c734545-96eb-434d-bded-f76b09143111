import pandas as pd
import numpy as np
from openpyxl import load_workbook

def pred(df):
    new_rows=[]
    current_time = None

    for index, row in df.iterrows():
        last_time = current_time
        current_time = row['时间 (time)']

        if last_time != current_time:
            new_row = df.iloc[index].copy()
            new_rows.append(new_row)

    return pd.DataFrame(new_rows).reset_index(drop=True)

def build(result ,i):
    # 加载已有的Excel文件
    wb = load_workbook('data.xlsx')

    # 新建一个工作表，名为'Sheet2'
    ws = wb.create_sheet('EMR'+str(i))

    # 将'result'列表内容写入工作表
    for row, value in enumerate(result, start=1):
        ws.cell(row=row, column=1, value=value['电磁辐射 (EMR)'])
        ws.cell(row=row, column=1, value=value['时间 (time)'])

    # 保存更改
    wb.save('data.xlsx')

file = pd.read_excel('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\附件3 (Attachment 3).xlsx',sheet_name='EMR')
for i in range(5):
    df=file.iloc[1:, i*2:i*2+2]
    build(pred(df),i)