import pandas as pd
from datetime import datetime, timedelta

# 请将此处的'your_file_path.xlsx'替换为您的Excel文件路径
file_name = '../../data.xlsx'
sheet_name = 'EMR_A'

# 读取Excel文件
df = pd.read_excel(file_name, sheet_name=sheet_name)

# 确保时间列名为'time'
df.rename(columns=lambda x: x.strip(), inplace=True)

# 将时间字符串转换为datetime对象
df['time'] = pd.to_datetime(df['时间 (time)'])

# 初始化变量
intervals = []
current_start = df['time'].iloc[0]
min_interval = None
total_duration = timedelta()
#NowClass=df['类别 (class)'].iloc[0]

# 遍历时间列表，计算时间区间
for i in range(1, len(df)):
    diff = df['time'].iloc[i] - df['time'].iloc[i-1]
    if diff > timedelta(days=1):  # 假设超过60秒则视为不同时间区间
        interval_end = df['time'].iloc[i-1]
        intervals.append((current_start, interval_end, interval_end - current_start))
        total_duration += (interval_end - current_start)

        # 更新最小时间区间
        if min_interval is None or (interval_end - current_start) < min_interval[2]:
            min_interval = (current_start, interval_end, interval_end - current_start)

        # 设置下一个时间区间的开始
        current_start = df['time'].iloc[i]
        #NowClass=df['类别 (class)'].iloc[i]

# 添加最后一个时间区间
interval_end = df['time'].iloc[-1]
intervals.append((current_start, interval_end, interval_end - current_start))
total_duration += (interval_end - current_start)

# 更新最小时间区间
if min_interval is None or (interval_end - current_start) < min_interval[2]:
    min_interval = (current_start, interval_end, interval_end - current_start)

# 计算平均时间区间长度
average_duration = total_duration / len(intervals) if intervals else timedelta()

# 输出结果
for interval in intervals:
    print(f"区间开始: {interval[0]}, 区间结束: {interval[1]}, 区间长度: {interval[2]}")

print(f"最小时间区间开始: {min_interval[0]}, 结束: {min_interval[1]}, 长度: {min_interval[2]}")
print(f"平均时间区间长度: {average_duration}")
