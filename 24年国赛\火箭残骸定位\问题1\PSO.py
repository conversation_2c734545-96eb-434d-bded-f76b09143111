import numpy as np
from itertools import permutations
import random

#匹配时间数据，寻找最大可能
list_count = {}
data = np.array([
    [110.241, 27.204, 824, 100.767],
    [110.780, 27.456, 727, 112.220],
    [110.712, 27.785, 742, 188.020],
    [110.251, 27.825, 850, 258.985],
    [110.524, 27.617, 786, 118.443],
    [110.467, 27.921, 678, 266.871],
    [110.047, 27.121, 575, 163.024]
])

# 地理坐标转换为距离的参数
jin = 97304
wei = 111263
v = 340


def function(x, y, z, t):
    error = 0
    for i in range(7):
        distance = np.sqrt(
            (jin * (x - data[i][0])) ** 2 + (wei * (y - data[i][1])) ** 2 + (z - data[i][2]) ** 2)
        error += abs(distance / v - data[i][3] + t)
    return 1/(1+error)


x_min = 109
x_max = 112
y_min = 26
y_max = 29
z_min = 0
z_max = 20000
t_min = -150
t_max = 150
# 速度上下限必须互为相反数，保证方向的随机性
v_min, v_max = [-0.3, -0.3, -2000, -30], [0.3, 0.3 ,2000 ,30]
# c2越大越容易收敛
w_min, w_max = 0.4,0.9
c1, c2 = 1.5, 1.5

Iter_num, Size = 3000, 80


bestx =110.68344877803186
besty =27.060243221284654
bestz =1056.8785176582237
bestt =-20.20756617390603

class Particle:
    def __init__(self):
        # 当前点的坐标
        '''
        self.x = random.uniform(100.5, 120.8)
        self.y = random.uniform(26, 29)
        self.z = random.uniform(100, 1000)
        self.t = random.uniform(-150, 150)
        '''
        self.x =random.uniform(x_min, x_max)
        self.y =random.uniform(y_min, y_max)
        self.z =random.uniform(z_min, z_max)
        self.t =random.uniform(t_min, t_max)

        # 记录局部最优点和最优值
        self.x_local_best = self.x
        self.y_local_best = self.y
        self.z_local_best = self.z
        self.t_local_best = self.t
        self.f_local_best = function(self.x, self.y, self.z, self.t)
        # 当前点的前进速度
        self.vx = random.uniform(v_min[0], v_max[0])
        self.vy = random.uniform(v_min[1], v_max[1])
        self.vz = random.uniform(v_min[2], v_max[2])
        self.vt = random.uniform(v_min[3], v_max[3])


class PSO:
    def __init__(self, iter_num, size):
        # size个点
        self.particles = [Particle() for _ in range(size)]
        # 记录全局最优点和最优值
        self.x_global_best =  bestx
        self.y_global_best =  besty
        self.z_global_best =  bestz
        self.t_global_best =  bestt
        self.f_global_best = function(self.x_global_best, self.y_global_best, self.z_global_best, self.t_global_best)
        # 迭代轮数
        self.iter_num = iter_num
        # 用来作图
        #self.f_global_best_records = []

    def update_v(self, particle, w):
        vx_new = w * particle.vx + \
                    c1 * np.random.rand() * (particle.x_local_best - particle.x) + \
                    c2 * np.random.rand() * (self.x_global_best - particle.x)
        vy_new = w * particle.vy + \
                    c1 * np.random.rand() * (particle.y_local_best - particle.y) + \
                    c2 * np.random.rand() * (self.y_global_best - particle.y)
        vz_new = w * particle.vz + \
                 c1 * np.random.rand() * (particle.z_local_best - particle.z) + \
                 c2 * np.random.rand() * (self.z_global_best - particle.z)
        vt_new = w * particle.vt + \
                 c1 * np.random.rand() * (particle.t_local_best - particle.t) + \
                 c2 * np.random.rand() * (self.t_global_best - particle.t)
        particle.vx = max(min(vx_new, v_max[0]), v_min[0])
        particle.vy = max(min(vy_new, v_max[1]), v_min[1])
        particle.vz = max(min(vz_new, v_max[2]), v_min[2])
        particle.vt = max(min(vt_new, v_max[3]), v_min[3])

    def update_xyzt(self, particle):
        x_new, y_new, z_new, t_new = particle.x + particle.vx, particle.y + particle.vy, particle.z + particle.vz, particle.t + particle.vt
        x_new, y_new = max(min(x_new, x_max), x_min), max(min(y_new, y_max), y_min)
        z_new, t_new = max(min(z_new, z_max), z_min), max(min(t_new, t_max), t_min)
        f_new = function(x_new, y_new, z_new, t_new)
        particle.x, particle.y, particle.z, particle.t = x_new, y_new, z_new, t_new

        if f_new > particle.f_local_best:
            particle.x_local_best = particle.x
            particle.y_local_best = particle.y
            particle.z_local_best = particle.z
            particle.t_local_best = particle.t
            particle.f_local_best = f_new
        if f_new > self.f_global_best:
            self.x_global_best = particle.x
            self.y_global_best = particle.y
            self.z_global_best = particle.z
            self.t_global_best = particle.t
            self.f_global_best = f_new


    def update(self):
        for _ in range(self.iter_num):
            #x, y, f = [], [], []
            w = w_max - (w_max - w_min) * _ / self.iter_num
            for particle in self.particles:
                self.update_v(particle, w)
                self.update_xyzt(particle)
            if _ % 100 ==0:
                print(_, self.f_global_best)

                #x.append(particle.x)
                #y.append(particle.y)
                #f.append(function(particle.x, particle.y))
            #yield x, y, f
            #self.f_global_best_records.append(self.f_global_best)


def main():
    global bestx, besty, bestz, bestt
    iter_num, size = Iter_num, Size
    while True:
        pso = PSO(iter_num, size)
        pso.update()
        bestx = pso.x_global_best
        besty = pso.y_global_best
        bestz = pso.z_global_best
        bestt = pso.t_global_best
        print('result coordinate: \n', pso.x_global_best, '\n', pso.y_global_best, '\n', pso.z_global_best, '\n',
              pso.t_global_best, '\n', pso.f_global_best)
        if pso.f_global_best > 0.8:
            break


if __name__ == '__main__':
    main()
