import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score
import numpy as np
from datetime import datetime
import joblib

# 加载提供的Excel文件
data_file_path = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\预处理\smoothed_data_AE.xlsx'

# 将Excel文件读入pandas DataFrame
data_df = pd.read_excel(data_file_path)

data_df = data_df[data_df['类别 (class)'] != 'D/E']

# 将'时间'列转换为datetime格式
data_df['时间'] = pd.to_datetime(data_df['时间 (time)'])

# 使用LabelEncoder对'类别'列进行编码
label_encoder = LabelEncoder()
data_df['类别_encoded'] = label_encoder.fit_transform(data_df['类别 (class)'])

#5.11
i=5
j=11

# 特征工程：添加滞后特征
# 假设我们添加过去1天和过去7天的电磁辐射作为特征
data_df['EMR_lag1'] = data_df['AE_smoothed'].shift(i)
data_df['EMR_lag2'] = data_df['AE_smoothed'].shift(i*j)

# 处理NaN值（由于滞后特征产生）
data_df.fillna(0, inplace=True)

# 更新特征列表
features = ['AE_smoothed', 'EMR_lag1', 'EMR_lag2']
target = ['类别_encoded']

# 分割数据为训练集和测试集，注意保持时间顺序
# 这里我们使用时间分割，而不是随机分割
split_date = '2019/8/1 0:00:00'  # 假设这是一个合适的时间分割点
train_df = data_df[data_df['时间'] < split_date]
test_df = data_df[data_df['时间'] >= split_date]

X_train, y_train = train_df[features], train_df[target]
X_test, y_test = test_df[features], test_df[target]

# 初始化随机森林分类器
rf_classifier = RandomForestClassifier(n_estimators=200, random_state=42)

# 训练模型
rf_classifier.fit(X_train, y_train.values.ravel())

# 在测试集上进行预测
y_pred = rf_classifier.predict(X_test)

# 计算模型的准确率
accuracy = accuracy_score(y_test, y_pred)
print(f'测试集上的准确率: {accuracy}')

# 保存训练好的模型
model_file_path = 'trained_random_forest_model.pkl'
joblib.dump(rf_classifier, model_file_path)
print(f'模型已保存至: {model_file_path}')
