import pandas as pd
import numpy as np

def load_data(data_path = 'normalize_data.xlsx', import_path = '引用.xlsx'):
    data = pd.read_excel(data_path)
    df = pd.read_excel(import_path)
    X_name = df['自变量']
    df_cleaned = df.dropna(subset=['因变量'])
    # 提取清理后的自变量和因变量
    y_name = df_cleaned['因变量']

    train = data[data['year']<=2016]
    test = data[data['year']==2020]
    pred = data[data['year']==2024]

    X_train = train[X_name]
    X_test = test[X_name]
    y_train = train[y_name]
    y_test = test[y_name]
    X_pred = pred[X_name]

    return X_train, X_test, y_train, y_test, X_pred

if __name__ == "__main__":
    X_train, X_test, y_train, y_test, X_pred = load_data('normalize_data.xlsx', '引用.xlsx')
    print(X_pred)