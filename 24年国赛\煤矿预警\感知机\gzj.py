import pandas as pd
from sklearn.linear_model import Perceptron
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import joblib

# 读取Excel文件
def read_excel(file_path):
    return pd.read_excel(file_path)

# 训练感知机模型
def train_perceptron(train_data, train_labels):
    model = Perceptron(random_state=42)
    model.fit(train_data, train_labels)
    return model

# 保存模型
def save_model(model, model_path):
    joblib.dump(model, model_path)

# 主函数
def main():
    # 读取数据
    train_df = read_excel('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_train.xlsx')
    val_df = read_excel('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_val.xlsx')
    test_df = read_excel('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_test.xlsx')

    # 数据预处理
    X_train = train_df[['Avg EMR', 'Var EMR', 'Spectral Entropy']].values
    y_train = (train_df['Category'] == 'C').astype(int).values

    X_val = val_df[['Avg EMR', 'Var EMR', 'Spectral Entropy']].values
    y_val = (val_df['Category'] == 'C').astype(int).values

    X_test = test_df[['Avg EMR', 'Var EMR', 'Spectral Entropy']].values
    y_test = (test_df['Category'] == 'C').astype(int).values

    # 特征标准化
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_val = scaler.transform(X_val)
    X_test = scaler.transform(X_test)

    # 训练模型
    model = train_perceptron(X_train, y_train)

    # 验证和测试模型（这里简化处理，实际应用中应更详细地评估模型性能）
    val_accuracy = model.score(X_val, y_val)
    test_accuracy = model.score(X_test, y_test)
    print(f'Validation Accuracy: {val_accuracy:.2f}')
    print(f'Test Accuracy: {test_accuracy:.2f}')

    # 保存模型
    save_model(model, 'perceptron_model.pkl')

if __name__ == '__main__':
    main()
