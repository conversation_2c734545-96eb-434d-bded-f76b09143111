import pandas as pd
import numpy as np
from scipy.fft import fft
import matplotlib.pyplot as plt
from scipy.stats import entropy
from openpyxl import load_workbook
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings("ignore")

# 假设Excel文件名为"data.xlsx"
file_path = '处理后附件2.xlsx'  # 替换为您的 Excel 文件路径



df=pd.read_excel(file_path, sheet_name='AE')
data_df = df['声波强度 (AE)']
time_df = df['时间 (time)']
firstIndex=0
lastIndex=0
lenth=len(time_df)

# 假设采样率为sample_rate（根据实际情况调整）
sample_rate = 1000  # 例如，1000 Hz

results = []

#计算特征
def features(firstIndex,lastIndex):
    # 计算FFT
    fft_result = fft(data_df[firstIndex:lastIndex].values)
    # 计算频谱熵,平均值，方差
    spectral_entropy = entropy(np.abs(fft_result) ** 2)
    avg = sum(data_df[firstIndex:lastIndex]) / len(data_df[firstIndex:lastIndex])
    var = sum((x - avg) ** 2 for x in data_df[firstIndex:lastIndex]) / len(data_df[firstIndex:lastIndex])
    return avg,var,spectral_entropy

#预测
def predict_category(features, model_file='D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、2\SVM\AE\model.pkl'):
    # 加载模型
    model = joblib.load(model_file)

    # 假设features是一个包含三个元素的列表或数组
    # 我们需要将其转换为与训练数据相同的格式（标准化）
    from sklearn.preprocessing import StandardScaler
    scaler = joblib.load('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、2\SVM\AE\scaler.joblib')  # 假设我们在准备模块中也保存了scaler
    features_scaled = scaler.transform([features])

    # 预测
    prediction = model.predict(features_scaled)
    # 由于我们只关心是否为C类，所以直接返回0或1
    return prediction[0]

num=0
index=0
while index < lenth - 1:
    print(index/lenth*100,'%')
    if time_df.iloc[lenth-1] - time_df.iloc[index]<pd.Timedelta(hours=1):
        break
    lastIndex=index
    #最小值
    while True:
        lastIndex+=25
        if lastIndex>=lenth or time_df.iloc[lastIndex] - time_df.iloc[index]>=pd.Timedelta(hours=1):
            break
    find=0  #0为未找到，1为找到，2为找到最大值
    if num >= 5:
        break
    #寻找
    while True:
        feature=features(index,lastIndex)
        isC=predict_category(feature)
        if isC==0:
            if find==0:
                if time_df.iloc[lastIndex] - time_df.iloc[index] >= pd.Timedelta(days=1) or lastIndex == lenth - 1:
                    break
                lastIndex += 1
            else:
                find = 2
                num += 1
                print(num)
                results.append([time_df.iloc[index], time_df.iloc[lastIndex], feature])  # 开始，结束，三特征
                index = lastIndex
                break
        else:
            if time_df.iloc[lastIndex] - time_df.iloc[index] >= pd.Timedelta(days=1) or lastIndex==lenth-1:
                find = 2
                num += 1
                print(num)
                results.append([time_df.iloc[index], time_df.iloc[lastIndex], feature])  # 开始，结束，三特征
                index = lastIndex
                break
            lastIndex += 1
            find = 1

    if find==2:
        index=lastIndex
    else:
        index+=50


print(results)

