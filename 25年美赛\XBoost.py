import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, classification_report, ConfusionMatrixDisplay
import xgboost as xgb
import matplotlib.pyplot as plt

def load_data(data_path = 'normalize_data.xlsx', import_path = '引用.xlsx'):
    data = pd.read_excel(data_path)
    df = pd.read_excel(import_path)
    X_name = df['自变量']
    df_cleaned = df.dropna(subset=['因变量'])
    # 提取清理后的自变量和因变量
    y_name = df_cleaned['因变量']

    train = data[data['year']<=2016]
    test = data[data['year']==2020]
    pred = data[data['year']==2024]

    X_train = train[X_name].values
    X_test = test[X_name].values
    y_train = train[y_name].values
    y_test = test[y_name].values
    X_pred = pred[X_name].values

    return X_train, X_test, y_train, y_test, X_pred

# Step 3: 模型训练
def train_xgboost(X_train, y_train):
    xgb_model = xgb.XGBClassifier(use_label_encoder=False, eval_metric="logloss")

    param_grid = {
        "n_estimators": [50, 100, 200],
        "max_depth": [3, 4, 5],
        "learning_rate": [0.01, 0.1, 0.2],
        "subsample": [0.8, 1],
        "colsample_bytree": [0.8, 1]
    }

    grid_search = GridSearchCV(
        estimator=xgb_model,
        param_grid=param_grid,
        scoring="accuracy",
        cv=3,
        verbose=1,
        n_jobs=-1
    )
    grid_search.fit(X_train, y_train)
    print("最佳超参数: ", grid_search.best_params_)
    return grid_search.best_estimator_


# Step 4: 模型评估
def evaluate_model(model, X_test, y_test):
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"模型准确率: {accuracy:.4f}")
    print("分类报告: \n", classification_report(y_test, y_pred))

    # 混淆矩阵
    ConfusionMatrixDisplay.from_estimator(model, X_test, y_test, cmap="Blues")
    plt.title("Confusion Matrix - XGBoost")
    plt.show()

    return y_pred


# Step 5: 特征重要性可视化
def plot_feature_importance(model, features):
    feature_importances = model.feature_importances_
    sorted_idx = np.argsort(feature_importances)[::-1]
    plt.figure(figsize=(8, 5))
    plt.bar(range(len(sorted_idx)), feature_importances[sorted_idx], align="center", color="skyblue")
    plt.xticks(range(len(sorted_idx)), np.array(features)[sorted_idx], rotation=45)
    plt.title("Feature Importance - XGBoost")
    plt.xlabel("Feature")
    plt.ylabel("Importance")
    plt.tight_layout()
    plt.show()


# 主程序
if __name__ == "__main__":
    X_train, X_test, y_train, y_test, X_pred = load_data('normalize_data.xlsx', '引用.xlsx')
    print('start training')
    xgboost_model = train_xgboost(X_train, y_train)
    evaluate_model(xgboost_model, X_test, y_test)
    plot_feature_importance(xgboost_model, features)
