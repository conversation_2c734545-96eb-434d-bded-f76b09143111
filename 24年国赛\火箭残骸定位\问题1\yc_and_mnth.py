import numpy as np
import random

# 定义遗传算法参数
POPULATION_SIZE = 200
CROSSOVER_RATE = 0.8
MUTATION_RATE = 0.2
MAX_GENERATIONS = 1000
INITIAL_TEMPERATURE = 1000  # 初始温度
COOLING_RATE = 0.99  # 冷却率

# 地理坐标转换为距离的参数
jin = 97304
wei = 111263
v = 340

# 数据点
data = np.array([
    [110.241, 27.204, 824, 100.767],
    [110.780, 27.456, 727, 112.220],
    [110.712, 27.785, 742, 188.020],
    [110.251, 27.825, 850, 258.985],
    [110.524, 27.617, 786, 118.443],
    [110.467, 27.921, 678, 266.871],
    [110.047, 27.121, 575, 163.024]
])

# 适应度函数
def fitness(individual):
    x, y, z, t = individual
    error = 0
    for i in [0, 1, 3, 6]:
        distance = np.sqrt((jin * (x - data[i, 0]))**2 + (wei * (y - data[i, 1]))**2 + (z - data[i, 2])**2)
        time = data[i, 3]
        error += abs(distance/v - time + t)
    return 1/(1+error)

# 初始化种群
def initialize_population():
    population = []
    for _ in range(POPULATION_SIZE):
        x = random.uniform(100.5, 120.8)
        y = random.uniform(26, 29)
        z = random.uniform(100, 1000)
        t = random.uniform(-150, 150)
        population.append((x, y, z, t))
    return population

# 选择操作
def selection(population):
    fitness_values = [fitness(individual) for individual in population]
    total_fitness = sum(fitness_values)
    selection_prob = [f / total_fitness for f in fitness_values]
    selected_indices = np.random.choice(len(population), size=POPULATION_SIZE, p=selection_prob)
    return [population[i] for i in selected_indices]

# 交叉操作
def crossover(parent1, parent2):
    if random.random() < CROSSOVER_RATE:
        child1 = (parent1[0], parent2[1], parent1[2], parent2[3])
        child2 = (parent2[0], parent1[1], parent2[2], parent1[3])
        return child1, child2
    else:
        return parent1, parent2

# 变异操作
def mutate(individual):
    if random.random() < MUTATION_RATE:
        individual = list(individual)
        mutation_index = random.randint(0, 3)
        if mutation_index == 0:
            individual[mutation_index] = random.uniform(100.5, 120.8)
        elif mutation_index == 1:
            individual[mutation_index] = random.uniform(26, 29)
        elif mutation_index == 2:
            individual[mutation_index] = random.uniform(100, 1000)
        else:
            individual[mutation_index] = random.uniform(-150, 150)
        return tuple(individual)
    else:
        return individual

# 模拟退火接受劣解的概率
def acceptance_probability(delta_fitness, temperature):
    if delta_fitness < 0:
        return 1
    else:
        return np.exp(-delta_fitness / temperature)

# 遗传算法主函数
def genetic_algorithm_modified():
    best_individual = None
    population = initialize_population()
    temperature = INITIAL_TEMPERATURE

    for generation in range(MAX_GENERATIONS):
        new_population = []
        for _ in range(POPULATION_SIZE // 2):
            parent1, parent2 = random.sample(population, 2)
            child1, child2 = crossover(parent1, parent2)
            child1 = mutate(child1)
            child2 = mutate(child2)

            # 模拟退火接受劣解的概率
            delta_fitness1 = fitness(parent1) - fitness(child1)
            delta_fitness2 = fitness(parent2) - fitness(child2)
            accept_child1 = acceptance_probability(delta_fitness1, temperature)
            accept_child2 = acceptance_probability(delta_fitness2, temperature)

            if accept_child1 > random.random():
                new_population.append(child1)
            else:
                new_population.append(parent1)

            if accept_child2 > random.random():
                new_population.append(child2)
            else:
                new_population.append(parent2)

        population = selection(new_population)

        # 检查是否满足停止条件
        if best_individual is None or fitness(best_individual) < fitness(max(population, key=fitness)):
            best_individual = max(population, key=fitness)

        # 降温
        temperature *= COOLING_RATE

        if (generation+1) % 100 == 0:
            print(f"Generation {generation}: Best Fitness = {fitness(best_individual)}")

    return best_individual

    # 运行遗传算法
best_solution_modified = genetic_algorithm_modified()
print(best_solution_modified, fitness(best_solution_modified))
print('yc:',fitness((110.6803766744996, 27.06381561361218, 903.246968396906, -19.248864451657948)))