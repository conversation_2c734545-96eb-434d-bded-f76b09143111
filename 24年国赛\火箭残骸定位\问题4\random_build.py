import random
from itertools import combinations
data = [
        [110.241,27.204,824,[100.767,164.229,214.850,270.065]],
        [110.783,27.456,727,[92.453,112.220,169.362,196.583]],
        [110.762,27.785,742,[75.560,110.696,156.936,188.020]],
        [110.251,28.025,850,[94.653,141.409,196.517,258.985]],
        [110.524,27.617,786,[78.600,86.216,118.443,126.669]],
        [110.467,28.081,678,[67.274,166.270,175.482,266.871]],
        [110.047,27.521,575,[103.738,163.024,206.789,210.306]]
        ]

for i in range(7):
    for j in range(4):
        data[i][3][j] += random.uniform(-0.5, 0.5)
print(data)

numbers = list(range(7))

# 生成所有从7个数中选取4个的排列组合
permutations_list = list(combinations(list(range(7)), 4))
print(permutations_list)
print(len(permutations_list))