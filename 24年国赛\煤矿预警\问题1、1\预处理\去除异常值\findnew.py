import pandas as pd
from datetime import datetime, timedelta

# 请将此处的'your_file_path.xlsx'替换为您的Excel文件路径
file_name = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\预处理\去除异常值\processed_data_EMR.xlsx'
#sheet_name = 'EMR'

# 读取Excel文件
df = pd.read_excel(file_name)

# 确保时间列名为'time'，类别列名为'class'
df.rename(columns=lambda x: x.strip(), inplace=True)
df['time'] = pd.to_datetime(df['时间 (time)'])

# 准备一个字典来存储结果
results = {'A': [], 'B': [], 'C': [], 'D/E': []}

# 遍历数据
prev_time = None
prev_class = None
interval_start = None
emr_values = []

for index, row in df.iterrows():
    current_time = row['time']
    current_class = row['类别 (class)']
    current_emr = row['电磁辐射 (EMR)']

    # 检查是否是新的时间区间
    if prev_time is not None and (current_class != prev_class or (current_time - prev_time) > timedelta(days=1)):
        # 计算上一个时间区间的长度、平均值和方差
        if interval_start is not None:  # 确保interval_start已被赋值
            interval_duration = (prev_time - interval_start).total_seconds() / (60 * 60)
            avg_emr = sum(emr_values) / len(emr_values)
            var_emr = sum((x - avg_emr) ** 2 for x in emr_values) / len(emr_values)

            # 将结果存储在对应的类别中
            results[prev_class].append({
                'Start': interval_start,
                'End': prev_time,
                'Duration(hour)': interval_duration,
                'Avg EMR': avg_emr,
                'Var EMR': var_emr
            })

        # 重置区间
        interval_start = current_time
        emr_values = []

    # 添加当前数据到区间
    emr_values.append(current_emr)

    # 更新上一个时间和类别
    prev_time = current_time
    prev_class = current_class

# 处理最后一个时间区间
if interval_start is not None and prev_time is not None:  # 确保interval_start和prev_time都已赋值
    interval_duration = (prev_time - interval_start).total_seconds() / (60 * 60)
    avg_emr = sum(emr_values) / len(emr_values)
    var_emr = sum((x - avg_emr) ** 2 for x in emr_values) / len(emr_values)
    results[prev_class].append({
        'Start': interval_start,
        'End': prev_time,
        'Duration(hour)': interval_duration,
        'Avg EMR': avg_emr,
        'Var EMR': var_emr
    })

# 输出结果到Excel
with pd.ExcelWriter('Duration_EMR.xlsx') as writer:
    for class_name, intervals in results.items():
        # 替换不允许的字符
        safe_class_name = class_name.replace('/', '_')
        if intervals:
            df_class = pd.DataFrame(intervals)
            df_class.to_excel(writer, sheet_name=safe_class_name, index=False)