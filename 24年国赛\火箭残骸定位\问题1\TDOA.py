import numpy as np


def tdoa_to_distance(tdoa, speed_of_sound):
    """
    根据时间差到达（TDOA）和声速计算距离。
    :param tdoa: 时间差到达（秒）
    :param speed_of_sound: 声速（米/秒）
    :return: 距离（米）
    """
    return tdoa * speed_of_sound


def estimate_source_position(tdoa_values, speed_of_sound):
    """
    根据两个接收器的时间差到达（TDOA）值估计声源的位置。
    :param tdoa_values: 每个接收器相对于参考接收器的时间差到达值（秒）
    :param speed_of_sound: 声速（米/秒）
    :return: 声源位置（元组，格式为(x, y)）
    """
    # 假设两个接收器位于x轴上，距离为d
    # 声源位置可以表示为(x_source, 0)
    # 因此，我们可以通过简单的几何关系来估计声源的位置
    # tdoa = (x_source - d) / speed_of_sound
    # 因此，x_source = d + speed_of_sound * tdoa

    # 计算声源到参考接收器的距离
    d = tdoa_to_distance(tdoa_values[1], speed_of_sound)

    # 计算声源位置
    x_source = d + speed_of_sound * tdoa_values[0]

    return (x_source, 0)


# 示例数据
tdoa_values = [-0.1, 0.2]  # 第一个接收器比第二个接收器早0.1秒接收到信号
speed_of_sound = 343  # 空气中的声速（米/秒）

# 估计声源位置
source_position = estimate_source_position(tdoa_values, speed_of_sound)
print(f"Estimated source position: {source_position}")
