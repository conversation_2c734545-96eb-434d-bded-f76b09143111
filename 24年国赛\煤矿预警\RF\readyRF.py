import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import joblib

# 函数：读取Excel文件
def read_excel(file_path):
    return pd.read_excel(file_path)

# 函数：准备数据
def prepare_data(df):
    X = df[['Avg EMR', 'Var EMR', 'Spectral Entropy']]
    y = (df['Category'] == 'C').astype(int)
    return X, y

# 函数：训练模型
def train_model(X_train, y_train):
    model = RandomForestClassifier()
    model.fit(X_train, y_train)
    return model

# 函数：验证模型
def validate_model(model, X_val, y_val):
    y_pred = model.predict(X_val)
    return accuracy_score(y_val, y_pred)

# 函数：测试模型
def test_model(model, X_test, y_test):
    y_pred = model.predict(X_test)
    return accuracy_score(y_test, y_pred)

# 主函数：执行整个准备过程
def main():
    # 读取数据
    train_df = read_excel('train_set.xlsx')
    val_df = read_excel('val_set.xlsx')
    test_df = read_excel('test_set.xlsx')

    # 准备数据
    X_train, y_train = prepare_data(train_df)
    X_val, y_val = prepare_data(val_df)
    X_test, y_test = prepare_data(test_df)

    # 训练模型
    model = train_model(X_train, y_train)

    # 验证模型
    val_accuracy = validate_model(model, X_val, y_val)
    print(f'Validation Accuracy: {val_accuracy}')

    # 测试模型
    test_accuracy = test_model(model, X_test, y_test)
    print(f'Test Accuracy: {test_accuracy}')

    # 保存模型
    joblib.dump(model, 'random_forest_model.pkl')

if __name__ == '__main__':
    main()
