import joblib
import numpy as np
from sklearn.preprocessing import StandardScaler

# 加载模型
def load_model(model_path):
    return joblib.load(model_path)

# 分类函数
def classify_data(features, model):
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(np.array(features).reshape(1, -1))  # 将特征列表转换为NumPy数组
    prediction = model.predict(features_scaled)
    return int(prediction[0])

# 示例使用
def example_usage():
    model = load_model('perceptron_model.pkl')
    features = [90.15310218,2209.528039,1.38281654

]  # 示例特征
    result = classify_data(features, model)
    print(f'The data is classified as class C: {result}')

if __name__ == '__main__':
    example_usage()
