import pandas as pd
import numpy as np
from openpyxl import load_workbook

# 假设Excel文件名为"data.xlsx"
file1_path = '/\\煤矿预警\\问题1、1\\预处理\\smoothed_data_EMR.xlsx'  # 替换为您的 Excel 文件路径
file2_path = '/\\煤矿预警\\问题1、1\\时间区间\\Duration_EMR.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='D_E')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'D/E']
result = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)].copy()  # 使用.copy()来避免SettingWithCopyWarning

    # 将时间转换为pandas的datetime对象，然后转换为数值形式（例如，可以用时间戳表示）
    interval_data['时间 (time)'] = pd.to_datetime(interval_data['时间 (time)'])
    interval_data['TimeNumeric'] = (interval_data['时间 (time)'] - interval_data['时间 (time)'].min()) / np.timedelta64(1, 's')  # 将时间转换为自起始时间的秒数

    # 提取幅值数据
    amplitude_values = interval_data['EMR_smoothed'].values
    time_values = interval_data['TimeNumeric'].values

    # 将numpy数组转换为pandas Series
    amplitude_series = pd.Series(amplitude_values)
    time_series = pd.Series(time_values)

    # 计算增长速率
    growth_rates = amplitude_series.diff() / time_series.diff()

    # 计算平均增长速率
    average_growth_rate = growth_rates.mean()

    # 存储平均增长速率结果
    result.append(average_growth_rate)

    # 打印或存储平均增长速率结果
    #print(f'时间区间 {start_time} 至 {end_time} 的平均增长速率为: {average_growth_rate}')
'''
# 创建一个DataFrame来存储平均增长速率
average_growth_rate_df = pd.DataFrame(result, columns=['Average Growth Rate'])

# 假设您想要将数据保存到名为"average_growth_rates.xlsx"的Excel文件中
average_growth_rate_df.to_excel('average_growth_rates.xlsx', sheet_name="AE_A", index=False)
'''
# 加载已有的Excel文件
wb = load_workbook('average_growth_rates.xlsx')

# 新建一个工作表，名为'Sheet2'
ws = wb.create_sheet('EMR_D_E')

# 将'result'列表内容写入工作表
for row, value in enumerate(result, start=1):
    ws.cell(row=row, column=1, value=value)

# 保存更改
wb.save('average_growth_rates.xlsx')
