import numpy as np
from itertools import permutations
import random

#匹配时间数据，寻找最大可能
list_count = {}

data = np.array([
    [110.241,27.204,824,[158.7843568,197.8988778,127.3912292,237.3106349
]],
    [110.783,27.456,727,[65.54249944,88.36954662,42.06419593,73.02655416
]],
    [110.762,27.785,742,[78.96167209,190.7921848,116.2865941,43.87837483
]],
    [110.251,28.025,850,[173.2004945,328.037179,214.8828828,209.1362958
]]
],dtype=object)

# 地理坐标转换为距离的参数
jin = 97304
wei = 111263
v = 340

def min_abs_sum(A, B, t):
    # 获取A和B的所有可能排序
    A_perms = [A]  # 假设A不需要重新排序，因为我们将调整B
    B_perms = list(permutations(B))

    min_sum = float('inf')
    best_B_order = None

    # 遍历B的所有可能排序
    for b_order in B_perms:
        current_sum = 0
        # 计算当前排序下的绝对值之和
        for a, b, T in zip(A, b_order, t):
            current_sum += abs(a - b + T)

            # 更新最小值和最佳B的排序
        if current_sum < min_sum:
            min_sum = current_sum
            best_B_order = b_order

    return min_sum, list(best_B_order)

def count_lists(sublist):
    # 遍历 sublist 中的每个子列表
    for item in sublist:
        # 将子列表转换为元组，以便可以用作字典的键
        item_tuple = tuple(item)

        # 如果元组在字典中已存在，则增加其计数
        if item_tuple in list_count:
            list_count[item_tuple] += 1
        # 否则，将元组添加到字典中，并设置其计数为1
        else:
            list_count[item_tuple] = 1

def function(x, y, z, t, a=0):
    error = 0
    hang = []
    lie = []
    for i in [0, 1, 2, 3]:
        timex = []
        for j in range(4):
            distance = np.sqrt(
                (jin * (x[j] - data[i][0])) ** 2 + (wei * (y[j] - data[i][1])) ** 2 + (z[j] - data[i][2]) ** 2)
            timex.append(distance / v)
        time = data[i][3]
        min_sum, best_B_order = min_abs_sum(timex, time, t)
        error += min_sum
        hang.append(best_B_order)
    if a==1:
        for i in range(4):
            lie.append([hang[0][i], hang[1][i],hang[2][i],hang[3][i]])
        count_lists(lie)
        #print(lie)
    return 1/(1+error)


x_min = [110, 110, 110, 110]
x_max = [111, 111, 111, 111]
y_min = [26, 26, 26, 26]
y_max = [29, 29, 29, 29]
z_min = [500, 500, 500, 500]
z_max = [800, 800, 800, 800]
t_min = [-10, -10, -10, -10]
t_max = [10, 10, 10, 10]
# 速度上下限必须互为相反数，保证方向的随机性
v_min, v_max = [-0.1, -0.15, -30, -2], [0.1, 0.15 ,30 ,2]
# c2越大越容易收敛
w_min, w_max = 0.4,0.9
c1, c2 = 1.5, 1.5

bestx=[0,0,0,0]
besty=[0,0,0,0]
bestz=[0,0,0,0]
bestt=[0,0,0,0]
Iter_num, Size = 2000, 50

class Particle:
    def __init__(self):
        # 当前点的坐标
        self.x = [110.90509456063134, 110.94846153059689, 110.59757402452735, 110.6201910855155] #[random.uniform(x_min[i], x_max[i]) for i in range(4)]
        self.y = [27.682779095140337, 27.211572331195494, 27.58367985785789, 27.435733896656956]  #[random.uniform(y_min[i], y_max[i]) for i in range(4)]
        self.z =[random.uniform(z_min[i], z_max[i]) for i in range(4)]
        self.t =[random.uniform(t_min[i], t_max[i]) for i in range(4)]

        # 记录局部最优点和最优值
        self.x_local_best = self.x
        self.y_local_best = self.y
        self.z_local_best = self.z
        self.t_local_best = self.t
        self.f_local_best = function(self.x, self.y, self.z, self.t)
        # 当前点的前进速度
        self.vx = [random.uniform(v_min[0], v_max[0]) for _ in range(4)]
        self.vy = [random.uniform(v_min[1], v_max[1]) for _ in range(4)]
        self.vz = [random.uniform(v_min[2], v_max[2]) for _ in range(4)]
        self.vt = [random.uniform(v_min[3], v_max[3]) for _ in range(4)]


class PSO:
    def __init__(self, iter_num, size):
        # size个点
        self.particles = [Particle() for _ in range(size)]
        # 记录全局最优点和最优值
        self.x_global_best = bestx
        self.y_global_best = besty
        self.z_global_best = bestz
        self.t_global_best = bestt
        self.f_global_best = function(self.x_global_best, self.y_global_best, self.z_global_best, self.t_global_best)
        # 迭代轮数
        self.iter_num = iter_num
        # 用来作图
        #self.f_global_best_records = []

    def update_v(self, particle, w):
        vx_new = [0, 0, 0, 0]
        vy_new = [0, 0, 0, 0]
        vz_new = [0, 0, 0, 0]
        vt_new = [0, 0, 0, 0]
        for i in range(4):
            vx_new[i] = w * particle.vx[i] + \
                     c1 * np.random.rand() * (particle.x_local_best[i] - particle.x[i]) + \
                     c2 * np.random.rand() * (self.x_global_best[i] - particle.x[i])
            vy_new[i] = w * particle.vy[i] + \
                     c1 * np.random.rand() * (particle.y_local_best[i] - particle.y[i]) + \
                     c2 * np.random.rand() * (self.y_global_best[i] - particle.y[i])
            vz_new[i] = w * particle.vz[i] + \
                        c1 * np.random.rand() * (particle.z_local_best[i] - particle.z[i]) + \
                        c2 * np.random.rand() * (self.z_global_best[i] - particle.z[i])
            vt_new[i] = w * particle.vt[i] + \
                        c1 * np.random.rand() * (particle.t_local_best[i] - particle.t[i]) + \
                        c2 * np.random.rand() * (self.t_global_best[i] - particle.t[i])
            particle.vx[i] = max(min(vx_new[i], v_max[0]), v_min[0])
            particle.vy[i] = max(min(vy_new[i], v_max[1]), v_min[1])
            particle.vz[i] = max(min(vz_new[i], v_max[2]), v_min[2])
            particle.vt[i] = max(min(vt_new[i], v_max[3]), v_min[3])

    def update_xyzt(self, particle):
        x_new, y_new, z_new, t_new = [0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]
        for i in range(4):
            x_new[i], y_new[i], z_new[i], t_new[i] = particle.x[i] + particle.vx[i], particle.y[i] + particle.vy[i], particle.z[i] + particle.vz[i], particle.t[i] + particle.vt[i]
            x_new[i], y_new[i] = max(min(x_new[i], x_max[0]), x_min[0]), max(min(y_new[i], y_max[1]), y_min[1])
            z_new[i], t_new[i] = max(min(z_new[i], z_max[2]), z_min[2]), max(min(t_new[i], t_max[3]), t_min[3])
        f_new = function(x_new, y_new, z_new, t_new)
        particle.x, particle.y, particle.z, particle.t = x_new, y_new, z_new, t_new

        if f_new > particle.f_local_best:
            particle.x_local_best = particle.x
            particle.y_local_best = particle.y
            particle.z_local_best = particle.z
            particle.t_local_best = particle.t
            particle.f_local_best = f_new
        if f_new > self.f_global_best:
            self.x_global_best = particle.x
            self.y_global_best = particle.y
            self.z_global_best = particle.z
            self.t_global_best = particle.t
            self.f_global_best = f_new


    def update(self):
        for _ in range(self.iter_num):
            #x, y, f = [], [], []
            w = w_max - (w_max - w_min) * _ / self.iter_num
            for particle in self.particles:
                self.update_v(particle, w)
                self.update_xyzt(particle)
            if _ % 100 ==0:
                print(_, self.f_global_best)


def main():
    iter_num, size = Iter_num, Size
    while True:
        pso = PSO(iter_num, size)
        pso.update()
        if pso.f_global_best > 0.9:
            break
    print('result coordinate: \n', pso.x_global_best, '\n', pso.y_global_best, '\n', pso.z_global_best, '\n', pso.t_global_best, '\n', pso.f_global_best)



if __name__ == '__main__':

    main()
    '''
    function([110.49965749293871, 110.50035169742, 110.30001115020643, 110.70005881371472]
 ,[27.310723560454317, 27.951822354210297, 27.650003409772783, 27.64999009199553]
 ,[12148.022585245939, 12415.06453548604, 11466.060222960876, 13495.531148938679]
 ,[12.40027106329643, 12.051931004866304, 14.009092243188046, 14.969097673767584] ,1)
    function(  [110.49979508486697, 110.50005858840306, 110.29999392881547, 110.69947346361775]
 ,[27.310435512742416, 27.950308392253515, 27.649997568057504, 27.649963894127794]
 ,[12291.701674919641, 11685.016408705438, 11484.808011002173, 13303.325701392576]
 ,[12.244332480219331, 12.83929660663631, 13.994268230919102, 15.196885281817345] ,1)
    function( [110.49964040150365, 110.50040466163055, 110.30000236721904, 110.69949801213714]
 ,[27.310759537643577, 27.95206532776559, 27.65000043036762, 27.649965530748176]
 ,[12129.576865497987, 12524.984673472974, 11475.625388511437, 13311.11679564627]
 ,[12.42014806639137, 11.928175655638785, 14.001532015125099, 15.187644504743707] ,1)
    print(list_count)
'''
    print(function( [110.90508632757586, 110.94287478267432, 110.62018930323418, 110.59757378286675]
 ,[27.682775737215785, 27.21432635144464, 27.435735321423945, 27.583679856766324]
 ,[724.0737321723955, 500.0, 698.0301777084902, 601.4803346735943]
 ,[-8.998180176341371, -3.000110449146983, -4.99993993382068, -2.0000616446393282] ,1))
    print(list_count)
