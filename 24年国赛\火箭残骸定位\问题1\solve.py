import sympy as sp
import numpy as np
import random

# 定义变量
x, y, z = sp.symbols('x y z')  # 经纬度和高程
t = sp.symbols('t')

jin = 97304
wei = 111263
v = 340

#经度、维度、高程、时间
data = [[110.241, 27.204, 824, 100.767],
        [110.780, 27.456, 727, 112.220],
        [110.712, 27.785, 742, 188.020],
        [110.251, 27.825, 850, 258.985],
        [110.524, 27.617, 786, 118.443],
        [110.467, 27.921, 678, 266.871],
        [110.047, 27.121, 575, 163.024]]

a=2000
# 生成不等式函数
def inequalities_func(x, y, z, t):
    ineq_values = []
    for i in [0,1,3,6]:
        distance = np.sqrt((jin * (x - data[i][0]))**2 + (wei * (y - data[i][1]))**2 + (z - data[i][2])**2)
        time = data[i][3]
        ineq_values.append(distance <= v * time - v * t + a)
        ineq_values.append(distance >= v * time - v * t - a)
    return ineq_values

# 蒙特卡洛方法
num_samples = 1000000
valid_points = []
for _ in range(num_samples):

    x_val = random.uniform(110,112)
    y_val = random.uniform(27.0,27.4)
    z_val = random.uniform(1000,1100)
    t_val = random.uniform(-17,-15)
    if all(inequalities_func(x_val, y_val, z_val, t_val)):
        valid_points.append((x_val, y_val, z_val, t_val))

# 输出有效点的数量和示例
print(f"找到的有效点数量: {len(valid_points)}")
print("示例有效点:", valid_points)


# 计算平均值
if valid_points:
    avg_x = np.mean([point[0] for point in valid_points])
    avg_y = np.mean([point[1] for point in valid_points])
    avg_z = np.mean([point[2] for point in valid_points])
    avg_t = np.mean([point[3] for point in valid_points])
    print(f"四个解的平均值: (x={avg_x}, y={avg_y}, z={avg_z}, t={avg_t})")
else:
    print("没有找到有效的点来计算平均值。")