import numpy as np
import pandas as pd


def gm11(data, predict_steps=1):
    # 累加生成
    data_accum = np.cumsum(data)

    # 建立数据矩阵B和Y
    B = np.column_stack([data_accum[:-1], np.ones(len(data_accum) - 1)])
    Y = data_accum[1:].reshape(-1, 1)

    # 最小二乘法求解参数
    [[a], [b]] = np.dot(np.dot(np.linalg.inv(np.dot(B.T, B)), B.T), Y)

    # 建立灰色预测模型
    def model(x):
        return (data[0] - b / a) * np.exp(-a * x) + b / a

    # 预测
    predict_values = [model(i) for i in range(len(data), len(data) + predict_steps)]

    return predict_values


# 读取数据
data = pd.read_csv('..\附件\附件1.csv',encoding='gbk')
data['月份'] = pd.to_datetime(data['月份'])
# 调整2022年的日期，将时间加1年
data.loc[data['月份'].dt.year == 2022, '月份'] += pd.DateOffset(years=1)
data.set_index('月份', inplace=True)

# 确保数据根据月份排序
data.sort_index(inplace=True)

# 按品类分类
categories = data['品类'].unique()


# 季节性调整
def seasonal_adjustment(data):
    # 创建一个新数组来存储调整后的数据
    adjusted_data = np.zeros_like(data)

    # 对于每个数据点，减去3个月前的值
    for i in range(len(data)):
        if i >= 3:
            adjusted_data[i] = data[i] - data[i - 3]
        else:
            # 对于前3个月，没有足够的数据进行季节性调整，可以保持不变或使用其他方法
            adjusted_data[i] = data[i]

    return adjusted_data


# 预测
forecast = {}
for i in range(1,351):
    category_data = data[data['品类'] == 'category'+str(i)]['库存量']
    adjusted_data = seasonal_adjustment(category_data)
    a = gm11(adjusted_data, predict_steps=12)
    forecast['category'+str(i)] = a[9:12]

# 输出结果
forecast_df = pd.DataFrame(forecast, index=['2024-7-1', '2024-8-1', '2024-9-1'])
forecast_df.to_csv('category1_forecast.csv')
