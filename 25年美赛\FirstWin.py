import pandas as pd

# 读取 Excel 文件
athletes_df = pd.read_excel(r"C:\Users\<USER>\Desktop\2025_Problem_C_Data\首次获奖\summerOly_athletes.xlsx")
pred_count_df = pd.read_excel(r"C:\Users\<USER>\Desktop\2025_Problem_C_Data\首次获奖\pred_count.xlsx")

# 去除 pred_count.xlsx 中第2列为0的行
pred_count_df = pred_count_df[pred_count_df.iloc[:, 1] != 0]

# 筛选符合条件的行
filtered_rows = []

for _, row in pred_count_df.iterrows():
    athlete_name = row.iloc[0]
    athlete_records = athletes_df[athletes_df.iloc[:, 0] == athlete_name]
    
    # 检查是否所有奖牌记录都是 "No medal"
    if not athlete_records.empty and (athlete_records.iloc[:, 1] == "No medal").all():
        filtered_rows.append(row)

# 创建新 DataFrame
new_df = pd.DataFrame(filtered_rows)

# 保存到新的 Excel 文件
new_df.to_excel(r"C:\Users\<USER>\Desktop\new.xlsx", index=False)
