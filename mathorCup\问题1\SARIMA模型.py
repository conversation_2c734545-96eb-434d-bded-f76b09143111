import pandas as pd
from statsmodels.tsa.statespace.sarimax import SARIMAX

# 读取CSV文件
df = pd.read_csv('..\附件\附件1.csv',encoding='gbk')
# 将月份转换为datetime类型，并设置为索引
df['月份'] = pd.to_datetime(df['月份'])

# 调整2022年的日期，将时间加1年
df.loc[df['月份'].dt.year == 2022, '月份'] += pd.DateOffset(years=1)

df.set_index('月份', inplace=True)

# 确保数据按时间顺序排序
df.sort_index(inplace=True)

# 对每个品类分别建立SARIMA模型并进行预测
predictions = {}
for category in df['品类'].unique():
    # 提取当前品类的数据
    category_data = df[df['品类'] == category].drop('品类', axis=1)

    # 检查数据是否足够，如果不足9个月，则跳过
    if len(category_data) < 3:
        continue

    # 建立SARIMA模型（参数需要根据实际情况调整）
    model = SARIMAX(category_data, order=(1, 1, 1), seasonal_order=(1, 1, 1, 12))
    model_fit = model.fit(disp=0)

    # 预测未来3个月的数据
    forecast = model_fit.get_forecast(steps=12)
    forecast_df = forecast.conf_int(alpha=0.05)  # 获得预测的置信区间
    forecast_mean = forecast.predicted_mean  # 获得预测的平均值

    # 确保预测结果不为负数
    forecast_mean[forecast_mean < 0] = 0

    # 只选择9到12个月的数据
    forecast_10_to_12_mean = forecast_mean.iloc[9:12]

    # 将预测结果存储在字典中
    predictions[category] = forecast_10_to_12_mean

# 创建一个排序键，它提取列名中的数字部分
def extract_number(key):
    return int(key.split('category')[1])

# 使用 sorted() 函数和自定义的排序键函数来排序字典
predictions = dict(sorted(predictions.items(), key=lambda item: extract_number(item[0])))


# 将预测结果转换为DataFrame
forecast_df = pd.DataFrame(predictions)

# 重命名列名为月份
forecast_df.index = ['2024年7月库存量', '2024年8月库存量', '2024年9月库存量']


# 保存到CSV文件
forecast_df.to_csv('SARIMA预测结果.csv')

# 输出预测结果
for category, prediction in predictions.items():
    print(f"{category}的2024年10月至2025年1月库存量预测：")
    print(prediction)
    print("\n")
