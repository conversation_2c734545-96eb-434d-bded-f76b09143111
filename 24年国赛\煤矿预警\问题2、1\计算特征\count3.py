import pandas as pd
import numpy as np
from openpyxl import load_workbook
import scipy.stats as stats

# 假设Excel文件名为"data.xlsx"
file1_path = '/\\煤矿预警\\问题1、1\\预处理\\smoothed_data_AE.xlsx'  # 替换为您的 Excel 文件路径
file2_path = '/\\煤矿预警\\问题1、1\\时间区间\\Duration_AE.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='C')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'C']
result = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)].copy()  # 使用.copy()来避免SettingWithCopyWarning

    # 将时间转换为pandas的datetime对象，然后转换为数值形式（例如，可以用时间戳表示）
    interval_data['时间 (time)'] = pd.to_datetime(interval_data['时间 (time)'])
    interval_data['TimeNumeric'] = (interval_data['时间 (time)'] - interval_data['时间 (time)'].min()) / np.timedelta64(1, 's')  # 将时间转换为自起始时间的秒数

    # 提取幅值数据
    amplitude_values = interval_data['AE_smoothed'].values
    time_values = interval_data['TimeNumeric'].values

    # 使用scipy.stats.linregress进行直线拟合
    slope, intercept, r_value, p_value, std_err = stats.linregress(time_values, amplitude_values)

    # 存储斜率结果
    result.append(slope)

# 加载已有的Excel文件
wb = load_workbook('xielv.xlsx')

# 新建一个工作表，名为'Sheet2'
ws = wb.create_sheet('AE_C')

# 将'result'列表内容写入工作表
for row, value in enumerate(result, start=1):
    ws.cell(row=row, column=1, value=value)

# 保存更改
wb.save('xielv.xlsx')
