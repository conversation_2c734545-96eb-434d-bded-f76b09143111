import pandas as pd
from sklearn.svm import SVR
from sklearn.model_selection import train_test_split
import numpy as np
from sklearn.metrics import accuracy_score
# 读取CSV文件
df = pd.read_csv('..\附件\附件1.csv', encoding='gbk')

# 将月份转换为datetime类型，并设置为索引
df['月份'] = pd.to_datetime(df['月份'])
df.set_index('月份', inplace=True)

data = pd.DataFrame()
X = pd.DataFrame()
y = pd.DataFrame()
for i in range(1,351):
    data['category'+str(i)] = df[df['品类'] == 'category'+str(i)]['库存量']
    X['category'+str(i)] = data['category'+str(i)][data['category'+str(i)].index.year == 2023].sort_index()
    y['category'+str(i)] = data['category'+str(i)][data['category'+str(i)].index.year == 2022].sort_index()


#print(y[y.index.month==7])

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X.T, y[y.index.month==7].T, test_size=0.2, random_state=42)
print(X_train.shape)
# 创建SVM模型
model = SVR()

# 训练模型
model.fit(X_train, y_train)

# 预测测试集
predictions = {}

for i in range(1,351):
    # 预测下一个值
    next_month = model.predict(X['category'+str(i)].values.reshape(1, -1))  # 确保输入是二维的
    predictions['category'+str(i)]=next_month[0]  # 添加预测值到列表中

    # 更新输入数据
    X_test = X_test.iloc[1:]  # 移除第一行
    X_test = X_test.append(pd.Series(next_month[0], index=X_test.columns), ignore_index=True)  # 添加新的预测值
print(predictions,'\n')
