import pandas as pd
import numpy as np
from scipy.fft import fft
import matplotlib.pyplot as plt
from scipy.stats import entropy
from openpyxl import load_workbook

# 假设Excel文件名为"data.xlsx"
file1_path = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\预处理\smoothed_data_EMR.xlsx'  # 替换为您的 Excel 文件路径
file2_path = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\时间区间\Duration_EMR.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='D_E')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'D/E']

# 定义一个函数来计算并返回FFT结果
def calculate_fft(data, sample_rate):
    # FFT计算
    fft_result = fft(data)
    # 计算频率
    frequencies = np.fft.fftfreq(len(data)) * sample_rate
    # 返回FFT结果和对应的频率
    return fft_result, frequencies

# 假设采样率为sample_rate（根据实际情况调整）
sample_rate = 1000  # 例如，1000 Hz

# 存储所有时间区间的FFT结果和频谱熵
fft_results = []
spectral_entropies = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)]
    # 提取声波强度数据
    ae_values = interval_data['EMR_smoothed'].values
    # 计算FFT
    fft_result, frequencies = calculate_fft(ae_values, sample_rate)
    # 计算频谱熵
    spectral_entropy = entropy(np.abs(fft_result)**2)
    # 存储结果
    fft_results.append((fft_result, frequencies))
    spectral_entropies.append(spectral_entropy)

# 返回FFT结果和频谱熵（这里只返回了第一个时间区间的结果，可以根据需要调整）
fft_results[0] if fft_results else None, spectral_entropies
print(spectral_entropies)

'''
# 创建一个DataFrame来存储光谱熵
entropy_df = pd.DataFrame({
    'Spectral Entropy': spectral_entropies
})

# 假设您想要将数据保存到名为"spectral_entropies.xlsx"的Excel文件中
entropy_df.to_excel('spectral_entropies.xlsx',sheet_name="AE_A", index=False)
'''
''''''
# 加载已有的Excel文件
wb = load_workbook('spectral_entropies.xlsx')

# 新建一个工作表，名为'Sheet2'
ws = wb.create_sheet('EMR_D_E')


# 将'shang'列表内容写入工作表
for row, value in enumerate(spectral_entropies, start=1):
    ws.cell(row=row, column=1, value=value)

# 保存更改
wb.save('spectral_entropies.xlsx')