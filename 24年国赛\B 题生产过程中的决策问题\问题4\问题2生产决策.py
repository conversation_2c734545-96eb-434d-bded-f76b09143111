from itertools import product
import random
# 生成所有可能的组合
combinations = list(product([0, 1], repeat=4))

# 打印前5个列表作为示例
#print(combinations)
#print(len(combinations))
#决策
D = combinations
#次品率
'''
p = [(0.1,0.1,0.1),
     (0.2,0.2,0.2),
     (0.1,0.1,0.1),
     (0.2,0.2,0.2),
     (0.1,0.2,0.1),
     (0.05,0.05,0.05)]
'''
#购买单价
Cp1 = 4
Cp2 = 18
#检测成本
Cd = [(2,3,3),
     (2,3,3),
     (2,3,3),
     (1,1,2),
     (8,1,2),
     (2,3,3)]
#成品装配成本
Ca = 6
#调换损失
Cl = [6,6,30,30,10,10]
#拆解费用
Cr = [5,5,5,5,5,40]
#售价
Pr = 56

#零件检测成本  i为情况  j为决策
def ljjccb(i,j):
    return Cd[i][0]*D[j][0]+Cd[i][1]*D[j][1]
#组装后成品数量
def N(i,j):
    return (
            min((1-p[i][0]),(1-p[i][1]))*D[j][0]*D[j][1]+
            (1-p[i][0])*D[j][0]*(1-D[j][1])+
            (1-p[i][1])*D[j][1]*(1-D[j][0])+
            (1-D[j][0])*(1-D[j][1])
    )
#成品检测成本  i为情况  j为决策
def cpjccb(i,j):
    return N(i,j)*Cd[i][2]*D[j][2]
#成品装配成本  i为情况  j为决策
def cpzpcb(i,j):
    return N(i,j)*Ca
#多种决策影响后的成品次品率p4
def p4(i,j):
    return 1-(1-(1-D[j][0])*p[i][0])*(1-(1-D[j][1])*p[i][1])*(1-p[i][2])
#成品拆解成本  i为情况  j为决策
def cpcjcb(i,j):
    return N(i,j)*p4(i,j)*Cr[i]*D[j][3]
#成品调换成本  i为情况  j为决策
def cpdhcb(i,j):
    return N(i,j)*p4(i,j)*(1-D[j][2])*Cl[i]
#决策函数
def fjc(p1, p2):
    if p1<p2:
        return 1
    else:
        return 0
#零件购买成本  i为情况  j为决策
def ljgmcb(i,j):
    return (
            Cp1 + Cp2 -
            D[j][0] * (1-D[j][1]) * p[i][0] * Cp2 - D[j][1] * (1-D[j][0]) * p[i][1] * Cp1 - (fjc(p[i][0], p[i][1]) * Cp1 * abs(p[i][0] - p[i][1]) + (1 - fjc(p[i][0], p[i][1])) * Cp2 * abs(p[i][0] - p[i][1])) -
            N(i,j) * p4(i,j) * (Cp1+Cp2) * D[j][3]
    )
#总成本
def S(i,j):
    return ljjccb(i,j)+cpjccb(i,j)+cpzpcb(i,j)+cpcjcb(i,j)+cpdhcb(i,j)+ljgmcb(i,j)
#总收入
def W(i,j):
    return N(i,j)*(1-p4(i,j))*Pr
#总收益
def P(i,j):
    return W(i,j)-S(i,j)

#统计
def count_elements(lst):
    # 创建一个空字典来存储元素计数
    element_count = {}

    # 遍历列表中的每个元素
    for element in lst:
        # 如果元素已经在字典中，增加其计数
        if element in element_count:
            element_count[element] += 1
        # 否则，将元素添加到字典中，并设置计数为1
        else:
            element_count[element] = 1

    # 找出出现次数最多的元素
    most_frequent_element = max(element_count, key=element_count.get)

    return element_count, most_frequent_element

#蒙特卡洛
num = 20000
for i in range(6):
    lst = []
    for n in range(num):
        global p
        # 取样本n=140,95%信度,在p中，0.005=（0.024，0.100），0.1=（0.060，0.160），0.2=（0.142，0.274）
        p = [(random.uniform(0.060, 0.160), random.uniform(0.060, 0.160), random.uniform(0.060, 0.160)),
             (random.uniform(0.142, 0.274), random.uniform(0.142, 0.274), random.uniform(0.142, 0.274)),
             (random.uniform(0.060, 0.160), random.uniform(0.060, 0.160), random.uniform(0.060, 0.160)),
             (random.uniform(0.142, 0.274), random.uniform(0.142, 0.274), random.uniform(0.142, 0.274)),
             (random.uniform(0.060, 0.160), random.uniform(0.142, 0.274), random.uniform(0.060, 0.160)),
             (random.uniform(0.024, 0.100), random.uniform(0.024, 0.100), random.uniform(0.024, 0.100))]
        last = -100
        jc = []
        for j in range(16):
            a = P(i, j)
            if last < a:
                last = a
                jc = D[j]
        lst.append(jc)
        # print(last,jc)
    element_count, most_frequent_element = count_elements(lst)
    print(element_count, most_frequent_element)

