import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.fft import fft

# 假设Excel文件名为"data.xlsx"
excel_file = "data.xlsx"

# 读取Excel文件中的“data”和“time”工作表
file1_path = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\预处理\smoothed_data_AE.xlsx'  # 替换为您的 Excel 文件路径
file2_path = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\时间区间\Duration_AE.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='C')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'C']

# 定义一个函数来计算并返回FFT结果
def calculate_fft(data, sample_rate):
    # FFT计算
    fft_result = fft(data)
    # 计算频率
    frequencies = np.fft.fftfreq(len(data)) * sample_rate
    # 返回FFT结果和对应的频率
    return fft_result, frequencies

# 假设采样率为sample_rate（根据实际情况调整）
sample_rate = 1000  # 例如，1000 Hz

# 存储所有时间区间的FFT结果
fft_results = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)]
    # 提取声波强度数据
    ae_values = interval_data['AE_smoothed'].values
    # 计算FFT
    fft_result, frequencies = calculate_fft(ae_values, sample_rate)
    # 存储结果
    fft_results.append((fft_result, frequencies))

# 可选：绘制第一个时间区间的频谱图
if fft_results:
    fft_result, frequencies = fft_results[0]
    plt.plot(frequencies, np.abs(fft_result))
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Amplitude')
    plt.title('Frequency Spectrum of Interval Data')
    plt.show()

# 返回FFT结果（这里只返回了第一个时间区间的结果，可以根据需要调整）
fft_results[0] if fft_results else None
print(fft_results[0])
