import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report
import joblib
from sklearn.utils import resample

# 读取Excel文件
def load_data(file_path):
    data = pd.read_excel(file_path)
    # 排除'D/E'类别的数据
    data = data[data['Category'] != 'D/E']
    X = data[['Avg EMR', 'Var EMR', 'Spectral Entropy']]
    y = (data['Category'] == 'C').astype(int)  # 将C类标记为1，非C类标记为0
    return X, y

# 准备数据
def prepare_data(train_file, valid_file, test_file):
    X_train, y_train = load_data(train_file)
    X_valid, y_valid = load_data(valid_file)
    X_test, y_test = load_data(test_file)

    # 过采样
    X_train_resampled, y_train_resampled = resample(X_train[y_train == 1], y_train[y_train == 1], replace=True, n_samples=len(X_train[y_train == 0]), random_state=42)
    X_train = pd.concat([X_train, X_train_resampled])
    y_train = pd.concat([y_train, y_train_resampled])

    # 数据标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_valid_scaled = scaler.transform(X_valid)
    X_test_scaled = scaler.transform(X_test)

    return X_train_scaled, y_train, X_valid_scaled, y_valid, X_test_scaled, y_test, scaler

# 训练模型
def train_model(X_train, y_train):
    model = SVC(kernel='linear', probability=True)  # 使用线性核，开启概率预测
    model.fit(X_train, y_train)
    return model

# 保存模型
def save_model(model, file_path):
    joblib.dump(model, file_path)

# 主函数来整合以上步骤
def main():
    train_file = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_train.xlsx'
    valid_file = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_val.xlsx'
    test_file = 'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\数据集\EMR\EMR_test.xlsx'
    model_file = 'model.pkl'
    scaler_file = 'scaler.joblib'

    # 加载并准备数据
    X_train_scaled, y_train, X_valid_scaled, y_valid, X_test_scaled, y_test, scaler = prepare_data(train_file, valid_file, test_file)

    # 保存scaler，以便后续使用
    joblib.dump(scaler, scaler_file)

    # 训练模型
    model = train_model(X_train_scaled, y_train)

    # 验证模型
    y_valid_pred = model.predict(X_valid_scaled)
    valid_accuracy = accuracy_score(y_valid, y_valid_pred)
    valid_report = classification_report(y_valid, y_valid_pred)
    print(f"Validation Accuracy: {valid_accuracy}")
    print(f"Validation Classification Report:\n{valid_report}")

    # 测试模型（在实际应用中，你可能不会在训练脚本中直接测试模型，
    # 但为了演示，我们在这里包含它）
    y_test_pred = model.predict(X_test_scaled)
    test_accuracy = accuracy_score(y_test, y_test_pred)
    test_report = classification_report(y_test, y_test_pred)
    print(f"Test Accuracy: {test_accuracy}")
    print(f"Test Classification Report:\n{test_report}")

    # 保存模型
    save_model(model, model_file)

if __name__ == '__main__':
    main()
