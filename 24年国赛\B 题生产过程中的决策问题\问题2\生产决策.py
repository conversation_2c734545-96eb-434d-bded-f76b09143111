from itertools import product

# 生成所有可能的组合
combinations = list(product([0, 1], repeat=4))

# 打印前5个列表作为示例
print(combinations)
print(len(combinations))
#决策
D = combinations
#次品率
p = [(0.1,0.1,0.1),
     (0.2,0.2,0.2),
     (0.1,0.1,0.1),
     (0.2,0.2,0.2),
     (0.1,0.2,0.1),
     (0.05,0.05,0.05)]
#购买单价
Cp1 = 4
Cp2 = 18
#检测成本
Cd = [(2,3,3),
     (2,3,3),
     (2,3,3),
     (1,1,2),
     (8,1,2),
     (2,3,3)]
#成品装配成本
Ca = 6
#调换损失
Cl = [6,6,30,30,10,10]
#拆解费用
Cr = [5,5,5,5,5,40]
#售价
Pr = 56

#零件检测成本  i为情况  j为决策
def ljjccb(i,j):
    return Cd[i][0]*D[j][0]+Cd[i][1]*D[j][1]
#组装后成品数量
def N(i,j):
    return (
            min((1-p[i][0]),(1-p[i][1]))*D[j][0]*D[j][1]+
            (1-p[i][0])*D[j][0]*(1-D[j][1])+
            (1-p[i][1])*D[j][1]*(1-D[j][0])+
            (1-D[j][0])*(1-D[j][1])
    )
#成品检测成本  i为情况  j为决策
def cpjccb(i,j):
    return N(i,j)*Cd[i][2]*D[j][2]
#成品装配成本  i为情况  j为决策
def cpzpcb(i,j):
    return N(i,j)*Ca
#多种决策影响后的成品次品率p4
def p4(i,j):
    return 1-(1-(1-D[j][0])*p[i][0])*(1-(1-D[j][1])*p[i][1])*(1-p[i][2])
#成品拆解成本  i为情况  j为决策
def cpcjcb(i,j):
    return N(i,j)*p4(i,j)*Cr[i]*D[j][3]
#成品调换成本  i为情况  j为决策
def cpdhcb(i,j):
    return N(i,j)*p4(i,j)*(1-D[j][2])*Cl[i]
#决策函数
def fjc(p1, p2):
    if p1<p2:
        return 1
    else:
        return 0
#零件购买成本  i为情况  j为决策
def ljgmcb(i,j):
    return (
            Cp1 + Cp2 -
            D[j][0] * (1-D[j][1]) * p[i][0] * Cp2 - D[j][1] * (1-D[j][0]) * p[i][1] * Cp1 - (fjc(p[i][0], p[i][1]) * Cp1 * abs(p[i][0] - p[i][1]) + (1 - fjc(p[i][0], p[i][1])) * Cp2 * abs(p[i][0] - p[i][1])) -
            N(i,j) * p4(i,j) * (Cp1+Cp2) * D[j][3]
    )

#总成本
def S(i,j):
    return ljjccb(i,j)+cpjccb(i,j)+cpzpcb(i,j)+cpcjcb(i,j)+cpdhcb(i,j)+ljgmcb(i,j)
#总收入
def W(i,j):
    return N(i,j)*(1-p4(i,j))*Pr
#总收益
def P(i,j):
    return W(i,j)-S(i,j)

for i in range(6):
    print(f"第{i+1}种情况：")
    last = -100
    jc = []
    for j in range(16):
        a = P(i,j)
        print(a,D[j])
        if last<a:
            last = a
            jc = D[j]
    print('最佳策略：',last,jc)
