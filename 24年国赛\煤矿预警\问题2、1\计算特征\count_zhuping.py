import pandas as pd
import numpy as np
from openpyxl import load_workbook
from scipy.fft import fft, fftfreq

# 假设Excel文件名为"data.xlsx"
file1_path = '/\\煤矿预警\\问题1、1\\预处理\\smoothed_data_EMR.xlsx'  # 替换为您的 Excel 文件路径
file2_path = '/\\煤矿预警\\问题1、1\\时间区间\\Duration_EMR.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='C')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'C']
result = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)].copy()

    # 确保时间列是datetime类型（虽然之前已经转换过，但这里再次确认）
    interval_data['时间 (time)'] = pd.to_datetime(interval_data['时间 (time)'])

    # 转换为数值形式
    interval_data['TimeNumeric'] = (interval_data['时间 (time)'] - interval_data['时间 (time)'].min()) / np.timedelta64(
        1, 's')

    # 提取幅值数据
    amplitude_values = interval_data['EMR_smoothed'].values

    # FFT分析
    N = len(amplitude_values)
    yf = fft(amplitude_values)
    xf = fftfreq(N, d=(interval_data['TimeNumeric'].iloc[-1] - interval_data['TimeNumeric'].iloc[0]) / N)

    # 计算振幅谱并找到最大振幅对应的频率
    amplitudes = np.abs(yf)
    max_amp_index = np.argmax(amplitudes[:N // 2])  # 只考虑正频率部分
    max_freq = xf[max_amp_index]
    max_amp = amplitudes[max_amp_index]

    # 存储结果
    result.append({ 'max_freq': max_freq, 'max_amp': max_amp})

    # 打印或存储平均增长速率结果
    #print(f'时间区间 {start_time} 至 {end_time} 的平均增长速率为: {average_growth_rate}')
'''
# 创建一个DataFrame来存储平均增长速率
average_growth_rate_df = pd.DataFrame(result)

# 假设您想要将数据保存到名为"average_growth_rates.xlsx"的Excel文件中
average_growth_rate_df.to_excel('max_freq_amp.xlsx', sheet_name="EMR_A", index=False)
'''
# 加载已有的Excel文件
wb = load_workbook('max_freq_amp.xlsx')

# 新建一个工作表，名为'Sheet2'
ws = wb.create_sheet('EMR_C')

# 将'result'列表内容写入工作表
for row, item in enumerate(result, start=1):
    ws.cell(row=row, column=1, value=item['max_freq'])
    ws.cell(row=row, column=2, value=item['max_amp'])

# 保存更改
wb.save('max_freq_amp.xlsx')
