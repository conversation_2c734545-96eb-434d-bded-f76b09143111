import numpy as np
import random
from itertools import permutations

# 定义遗传算法参数
POPULATION_SIZE = 100
CROSSOVER_RATE = 0.7
MUTATION_RATE = 0.5
MAX_GENERATIONS = 2000
INITIAL_TEMPERATURE = 10000  # 初始温度
COOLING_RATE = 0.9  # 冷却率

# 地理坐标转换为距离的参数
jin = 97304
wei = 111263
v = 340

#匹配时间数据，寻找最大可能
list_count = {}

# 数据点
data = np.array([
    [110.241,27.204,824,[100.767,164.229,214.850,270.065]],
    [110.783,27.456,727,[92.453,112.220,169.362,196.583]],
    #[110.762,27.785,742,[75.560,110.696,156.936,188.020]],
    [110.251,28.025,850,[94.653,141.409,196.517,258.985]],
    #[110.524,27.617,786,[78.600,86.216,118.443,126.669]],
    #[110.467,28.081,678,[67.274,166.270,175.48,266.871]],
    [110.047,27.521,575,[103.738,163.024,206.789,210.306]]
],dtype=object)

#最小误差
def min_abs_sum(A, B, t):
    # 获取A和B的所有可能排序
    A_perms = [A]  # 假设A不需要重新排序，因为我们将调整B
    B_perms = list(permutations(B))

    min_sum = float('inf')
    best_B_order = None

    # 遍历B的所有可能排序
    for b_order in B_perms:
        current_sum = 0
        # 计算当前排序下的绝对值之和
        for a, b, T in zip(A, b_order, t):
            current_sum += abs(a - b + T)

            # 更新最小值和最佳B的排序
        if current_sum < min_sum:
            min_sum = current_sum
            best_B_order = b_order

    return min_sum, list(best_B_order)

def count_lists(sublist):
    # 初始化一个空字典来存储子列表（作为元组）及其出现次数
    # 将子列表转换为元组，以便可以用作字典的键
    sublist_tuple = tuple(sublist)

    # 如果元组在字典中已存在，则增加其计数
    if sublist_tuple in list_count:
        list_count[sublist_tuple] += 1
        # 否则，将元组添加到字典中，并设置其计数为1
    else:
        list_count[sublist_tuple] = 1

# 适应度函数
def fitness(individual,a=0):
    x, y, z, t = individual[:4]
    error = 0
    for i in [0, 1, 2, 3]:
        timex=[]
        for j in range(4):
            distance = np.sqrt((jin * (individual[0][j] - data[i][0])) ** 2 + (wei * (individual[1][j] - data[i][1])) ** 2 + (individual[2][j] - data[i][2]) ** 2)
            timex.append(distance/v)
        time = data[i][3]
        min_sum, best_B_order = min_abs_sum(timex, time, t)
        error += min_sum
        if a==1:
            count_lists(best_B_order)
        elif a==2:
            print(best_B_order)
    return 1/(1+error)

# 初始化种群
def initialize_population():
    population = []
    for _ in range(POPULATION_SIZE):
        x = [random.uniform(105, 115) for _ in range(4)]
        y = [random.uniform(26, 29) for _ in range(4)]
        z = [random.uniform(700, 1000) for _ in range(4)]
        t = [random.uniform(-50, 20) for _ in range(4)]
        population.append((x, y, z, t))
    return population

# 选择操作
def selection(population):
    fitness_values = [fitness(individual) for individual in population]
    total_fitness = sum(fitness_values)
    selection_prob = [f / total_fitness for f in fitness_values]
    selected_indices = np.random.choice(len(population), size=POPULATION_SIZE, p=selection_prob)
    return [population[i] for i in selected_indices]

# 交叉操作
def crossover(parent1, parent2):
    if random.random() < CROSSOVER_RATE:
        child1 = (parent1[0], parent2[1], parent1[2], parent2[3])
        child2 = (parent2[0], parent1[1], parent2[2], parent1[3])
        return child1, child2
    else:
        return parent1, parent2

# 变异操作
def mutate(individual):
    if random.random() < MUTATION_RATE:
        individual = list(individual)
        mutation_index = random.randint(0, 3)
        if mutation_index == 0:
            individual[mutation_index][0] = random.uniform(110.2, 111.1)
            individual[mutation_index][1] = random.uniform(109.4, 110.4)
            individual[mutation_index][2] = random.uniform(110.3, 111.3)
            individual[mutation_index][3] = random.uniform(110.2, 111.2)
        elif mutation_index == 1:
            individual[mutation_index][0] = random.uniform(27.5, 28.5)
            individual[mutation_index][1] = random.uniform(27.2, 28.2)
            individual[mutation_index][2] = random.uniform(26.6, 27.5)
            individual[mutation_index][3] = random.uniform(27.4, 28.3)
        elif mutation_index == 2:
            individual[mutation_index][0] = random.uniform(850, 1000)
            individual[mutation_index][1] = random.uniform(850, 1000)
            individual[mutation_index][2] = random.uniform(900, 1080)
            individual[mutation_index][3] = random.uniform(700, 800)
        else:
            individual[mutation_index][0] = random.uniform(-14, 5)
            individual[mutation_index][1] = random.uniform(-30, 13)
            individual[mutation_index][2] = random.uniform(-20, 5)
            individual[mutation_index][3] = random.uniform(0, 15)
        return tuple(individual)
    else:
        return individual

# 模拟退火接受劣解的概率
def acceptance_probability(delta_fitness, temperature):
    if delta_fitness < 0:
        return 1
    else:
        return np.exp(-delta_fitness / temperature)

# 遗传算法主函数
def genetic_algorithm_modified():
    best_individual = None
    population = initialize_population()
    temperature = INITIAL_TEMPERATURE

    for generation in range(MAX_GENERATIONS):
        new_population = []
        for _ in range(POPULATION_SIZE // 2):
            parent1, parent2 = random.sample(population, 2)
            child1, child2 = crossover(parent1, parent2)
            child1 = mutate(child1)
            child2 = mutate(child2)

            # 模拟退火接受劣解的概率
            delta_fitness1 = fitness(parent1) - fitness(child1)
            delta_fitness2 = fitness(parent2) - fitness(child2)
            accept_child1 = acceptance_probability(delta_fitness1, temperature)
            accept_child2 = acceptance_probability(delta_fitness2, temperature)

            if accept_child1 > random.random():
                new_population.append(child1)
            else:
                new_population.append(parent1)

            if accept_child2 > random.random():
                new_population.append(child2)
            else:
                new_population.append(parent2)
        # 检查是否满足停止条件
        if generation==0 or fitness(best_individual) < fitness(max(new_population, key=fitness)):
            best_individual = max(new_population, key=fitness)

        population = selection(new_population)
        '''
        for individual in population:
            if generation==0 or fitness(individual) < fitness(best_individual):
                best_individual = individual
        '''
        # 降温
        temperature *= COOLING_RATE

        if (generation+1) % 20 == 0:
            print(f"Generation {generation}: Best Fitness = {fitness(best_individual,a=0)} {best_individual}")


    return best_individual

    # 运行遗传算法
best_solution_modified = genetic_algorithm_modified()
print(best_solution_modified, fitness(best_solution_modified,a=2))
#print(fitness(([109.9171068382935, 110.18631002113194, 111.24583537185089, 110.23144574150619], [28.224907625228514, 27.25444179216579, 27.135905790211023, 27.709498071031724], [905.1223328603771, 885.8476242091547, 755.7950392436439, 868.9923097550477], [-24.955340931752094, -25.34157470388716, -39.86530460678124, -1.9343920601275073])))
'''
fitness(([110.66868381922188, 110.02903221777473, 110.64155190843766, 110.9414628680735], [27.794537392759494, 27.847597381288086, 27.22084694760519, 27.74383198085838], [974.7389702736591, 873.6613735135454, 979.2468043443434, 730.9485515194642], [-12.791907984970475, -28.758263394022368, -18.2015000655195, 3.2600030293146034]),1)
fitness(([110.84899451158539, 109.80701858225099, 110.60359151446444, 110.55091384356398], [27.700717872072033, 27.567407303636962, 27.063155539399013, 27.9463066125026], [890.5179949994924, 889.3000775235182, 1037.8448863689102, 722.5750172943376], [-13.328224286479731, -18.064044437735383, -15.5903838899473, 1.0372520150991953]),1)
fitness(([110.20292795583363, 110.26243563390916, 110.53885723436382, 110.75129144218808], [27.84994316321902, 27.707769977534806, 27.106006341417586, 27.769572628460566], [876.9115427590832, 956.4804633832408, 1049.4041933100634, 736.8165191738246], [-0.3503447413784855, 11.876692103858616, 1.3796627656198979, 12.87029955812418]),1)
print(list_count)
'''
#print(list_count)
#print('yc:',fitness((110.6803766744996, 27.06381561361218, 903.246968396906, -19.248864451657948)))
#print("mtkl:",fitness((110.60763114374828, 27.122828974387147, 766.0079148704832, 0.019829812625790755)))
#print('三边：',fitness((110.63099151,27.15185963,963.38453026 ,-6.53563476)))