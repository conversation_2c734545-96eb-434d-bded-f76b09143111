import pandas as pd
import numpy as np
from sklearn.svm import SVR
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt  # 引入 matplotlib
from sklearn.multioutput import MultiOutputRegressor

def load_data(data_path = 'normalize_data.xlsx', import_path = '引用.xlsx'):
    data = pd.read_excel(data_path)
    df = pd.read_excel(import_path)
    X_name = df['自变量']
    df_cleaned = df.dropna(subset=['因变量'])
    # 提取清理后的自变量和因变量
    y_name = df_cleaned['因变量']

    train = data[data['year']<=2016]
    test = data[data['year']==2020]
    pred = data[data['year']==2024]

    X_train = train[X_name]
    X_test = test[X_name]
    y_train = train[y_name]
    y_test = test[y_name]
    X_pred = pred[X_name]

    return X_train, X_test, y_train, y_test, X_pred


def svm_regression(X_train, X_test, y_train, y_test, X_pred, output_path="svm_results2.xlsx"):
    """
    使用支持向量机进行多输入多输出回归拟合。

    Args:
        X_train (pd.DataFrame): 训练集输入特征.
        X_test (pd.DataFrame): 测试集输入特征.
        y_train (pd.DataFrame): 训练集输出目标.
        y_test (pd.DataFrame): 测试集输出目标.
        X_pred (pd.DataFrame): 预测集输入特征.
        output_path (str, optional): 结果输出的Excel文件路径. 默认为 "svm_results.xlsx".

    Returns:
        None (结果将保存在Excel文件中)
    """
    # 1. 数据预处理: 特征缩放
    scaler_X = StandardScaler()
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    X_pred_scaled = scaler_X.transform(X_pred)

    scaler_y = StandardScaler()
    y_train_scaled = scaler_y.fit_transform(y_train)

    # 2. 构建 SVR 模型
    svr = SVR(kernel='rbf')

    # 3. 使用 MultiOutputRegressor 包装 SVR 模型
    multi_output_svr = MultiOutputRegressor(svr)

    # 4. 参数网格
    param_grid = {
        'estimator__C': [0.1, 1, 10],
        'estimator__gamma': ['scale', 'auto', 0.1, 1],
        'estimator__epsilon': [0.01, 0.1, 0.2]
    }

    # 5. 使用 GridSearchCV 进行网格搜索，选择最佳参数
    grid_search = GridSearchCV(multi_output_svr, param_grid, cv=3, scoring='neg_mean_squared_error', verbose=2,
                               n_jobs=-1)
    grid_search.fit(X_train_scaled, y_train_scaled)

    best_multi_output_svr = grid_search.best_estimator_

    # 6. 模型训练
    print("Best params:", grid_search.best_params_)
    best_multi_output_svr.fit(X_train_scaled, y_train_scaled)

    # 7. 模型预测
    y_pred_scaled = best_multi_output_svr.predict(X_pred_scaled)
    y_pred = scaler_y.inverse_transform(y_pred_scaled)

    # 8. 评估模型
    y_test_scaled_pred = best_multi_output_svr.predict(X_test_scaled)
    y_test_pred = scaler_y.inverse_transform(y_test_scaled_pred)

    mse = mean_squared_error(y_test, y_test_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_test_pred)

    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")

    # 9. 特征重要性分析（针对每个输出分别进行特征选择）
    feature_importance_all = {}
    for i in range(y_train.shape[1]):
        # 计算所有特征的 f_regression 分数
        feature_scores = f_regression(X_train_scaled, y_train_scaled[:, i])[0]  # 只需要第一个返回值，第二个为p值
        feature_names = X_train.columns

        feature_importance = pd.DataFrame({
            'Feature': feature_names,
            'Score': feature_scores
        })

        feature_importance_all[f'output_{i}'] = feature_importance

    # 10. 输出结果到 Excel
    with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
        # 输出预测结果
        y_pred_df = pd.DataFrame(y_pred, columns=y_train.columns)
        y_pred_df.to_excel(writer, sheet_name='Predictions', index=False)

        # 输出评价指标
        metrics_df = pd.DataFrame({
            'Metric': ['MSE', 'RMSE', 'MAE'],
            'Value': [mse, rmse, mae]
        })
        metrics_df.to_excel(writer, sheet_name='Metrics', index=False)

        # 输出特征重要性
        for output_col, feature_importance_df in feature_importance_all.items():
            feature_importance_df.to_excel(writer, sheet_name=f"FeatureImportance_{output_col}", index=False)
    print(f"Results saved to {output_path}")


if __name__ == '__main__':
    X_train, X_test, y_train, y_test, X_pred = load_data('最新normalize_data.xlsx', '引用.xlsx')
    svm_regression(X_train, X_test, y_train, y_test, X_pred)