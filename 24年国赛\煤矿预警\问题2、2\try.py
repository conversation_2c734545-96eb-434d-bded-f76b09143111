import pandas as pd
import numpy as np
import joblib
import warnings
warnings.filterwarnings("ignore")
import scipy.stats as stats
from scipy.fft import fft
import matplotlib.pyplot as plt
from scipy.stats import entropy
from openpyxl import load_workbook
from datetime import datetime, timedelta

# 假设Excel文件名为"data.xlsx"
file_path = '处理后附件2_2.xlsx'  # 替换为您的 Excel 文件路径



df=pd.read_excel(file_path, sheet_name='AE')
data_df = df['声波强度 (AE)']
time_df = df['时间 (time)']
firstIndex=0
lastIndex=0
lenth=len(time_df)

# 假设采样率为sample_rate（根据实际情况调整）
sample_rate = 1000  # 例如，1000 Hz

results = []

#计算特征
def features(firstIndex,lastIndex):

    # 计算FFT
    fft_result = fft(data_df[firstIndex:lastIndex].values)
    # 计算频谱熵,平均值，方差
    spectral_entropy = entropy(np.abs(fft_result) ** 2)
    avg = sum(data_df[firstIndex:lastIndex]) / len(data_df[firstIndex:lastIndex])
    var = sum((x - avg) ** 2 for x in data_df[firstIndex:lastIndex]) / len(data_df[firstIndex:lastIndex])

    # 将时间转换为pandas的datetime对象，然后转换为数值形式（例如，可以用时间戳表示）
    interval_data = pd.to_datetime(time_df[firstIndex:lastIndex])
    TimeNumeric = (interval_data - interval_data.min()) / np.timedelta64( 1, 's')  # 将时间转换为自起始时间的秒数

    # 提取幅值数据
    amplitude_values = data_df[firstIndex:lastIndex].values
    time_values = TimeNumeric.values

    # 将numpy数组转换为pandas Series
    amplitude_series = pd.Series(amplitude_values)
    time_series = pd.Series(time_values)

    # 计算增长速率
    growth_rates = amplitude_series.diff() / time_series.diff()

    # 计算平均增长速率
    average_growth_rate = growth_rates.mean()

    acceleration_values = np.diff(amplitude_values, n=2)

    # 计算平均加速度
    average_acceleration = np.mean(acceleration_values) if len(acceleration_values) > 0 else 0

    # 使用scipy.stats.linregress进行直线拟合
    #slope, intercept, r_value, p_value, std_err = stats.linregress(time_values, amplitude_values)

    return avg,var,spectral_entropy,average_growth_rate,average_acceleration

#预测
def predict_category(features, model_file='D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题2、2\RF\AE\model.pkl'):
    # 加载模型
    model = joblib.load(model_file)

    # 假设features是一个包含三个元素的列表或数组
    # 我们需要将其转换为与训练数据相同的格式（标准化）
    from sklearn.preprocessing import StandardScaler
    scaler = joblib.load('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题2、2\RF\AE\scaler.joblib')  # 假设我们在准备模块中也保存了scaler
    features_scaled = scaler.transform([features])

    # 预测
    prediction = model.predict(features_scaled)
    # 由于我们只关心是否为C类，所以直接返回0或1
    return prediction[0]

num=0
index=0
while index < lenth - 1:
    print(index/lenth*100,'%')
    if time_df.iloc[lenth-1] - time_df.iloc[index]<pd.Timedelta(hours=0.5):
        break
    lastIndex=index
    #最小值
    lastIndex = time_df[(time_df - time_df.iloc[index]) >= pd.Timedelta(hours=1)].index.min()
    find=0  #0为未找到，1为找到，2为找到最大值
    if num >= 10:
        break
    #寻找
    while True:
        feature=features(index,lastIndex)
        isC=predict_category(feature)
        if isC==0:
            if find==0:
                if time_df.iloc[lastIndex] - time_df.iloc[index] >= pd.Timedelta(hours=12) or lastIndex == lenth - 1:
                    break
                lastIndex += 50
            else:
                find = 2
                num += 1
                print(num)
                results.append([time_df.iloc[index], time_df.iloc[lastIndex], feature])  # 开始，结束，三特征
                print(results)
                index = lastIndex
                break
        else:
            if time_df.iloc[lastIndex] - time_df.iloc[index] >= pd.Timedelta(hours=12) or lastIndex==lenth-1:
                find = 2
                num += 1
                print(num)
                results.append([time_df.iloc[index], time_df.iloc[lastIndex], feature])  # 开始，结束，三特征
                print(results)
                index = lastIndex
                break
            lastIndex += 50
            find = 1

    if find==2:
        index=lastIndex+1
    else:
        index+=50


print(results)

