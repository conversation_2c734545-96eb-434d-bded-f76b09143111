import pandas as pd

df = pd.read_excel('处理后附件2_2.xlsx', sheet_name='EMR')

def process_class_changes(df):
    new_rows = []
    current_class = None
    current_index = None
    current_time = None
    last_class = None
    last_time = None
    last_index = None

    for index, row in df.iterrows():

        last_class = current_class
        last_time = current_time
        last_index = current_index
        current_time = row['时间 (time)']
        current_index = index

        if last_time != current_time :
            new_row = df.iloc[index].copy()
            new_rows.append(new_row)


    return pd.DataFrame(new_rows).reset_index(drop=True)

new_df = process_class_changes(df)
new_df.to_excel('New_EMR.xlsx', index=False)
