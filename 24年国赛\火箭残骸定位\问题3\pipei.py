from itertools import permutations


def min_abs_sum(A, B):
    # 获取A和B的所有可能排序  
    A_perms = [A]  # 假设A不需要重新排序，因为我们将调整B  
    B_perms = list(permutations(B))

    min_sum = float('inf')
    best_B_order = None

    # 遍历B的所有可能排序  
    for b_order in B_perms:
        current_sum = 0
        # 计算当前排序下的绝对值之和  
        for a, b in zip(A, b_order):
            current_sum += abs(a - b)

            # 更新最小值和最佳B的排序
        if current_sum < min_sum:
            min_sum = current_sum
            best_B_order = b_order

    return min_sum, list(best_B_order)

list_count = {}

def count_lists(sublist):
    # 初始化一个空字典来存储子列表（作为元组）及其出现次数
    # 将子列表转换为元组，以便可以用作字典的键
    sublist_tuple = tuple(sublist)

    # 如果元组在字典中已存在，则增加其计数
    if sublist_tuple in list_count:
        list_count[sublist_tuple] += 1
        # 否则，将元组添加到字典中，并设置其计数为1
    else:
        list_count[sublist_tuple] = 1


    # 示例使用

'''
numbers_lists = [[1, 2, 3], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6]]
count_lists(numbers_lists[1])
count_lists(numbers_lists[1])
count_lists(numbers_lists[2])
# 打印结果，为了可读性，将元组转换回列表（如果需要）
for sublist_tuple, count in list_count.items():
    # 如果你想要以列表的形式打印它们，可以这样做：
    sublist = list(sublist_tuple)
    print(f"List [{', '.join(map(str, sublist))}]: {count}")

'''
# 示例
A = [3, 2, 3, 4]
B = [3, 4, 2, 2]
min_sum, best_B = min_abs_sum(A, B)
print(f"最小绝对值之和: {min_sum}")
print(f"对应的B的重新排序: {best_B}")
