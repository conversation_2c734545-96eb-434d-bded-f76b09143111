import pandas as pd

# 假设你的表格数据在一个CSV文件中
# 读取表格数据
data = pd.read_excel('ridge_results2.xlsx','Predictions')
df = pd.read_excel('引用.xlsx')
all = pd.read_excel('pred_count.xlsx')
county = df['NOC']

for i in range(233):
    all.at[county[i],'NOC'] = county[i]
    gold = 0
    silver = 0
    bronze = 0
    for j in range(39):
        gold+=data.at[i,df.at[j,'sports']+'_y_gold']
        silver+=data.at[i,df.at[j,'sports']+'_y_silver']
        bronze+=data.at[i,df.at[j,'sports']+'_y_bronze']
    '''if gold < 0:
        gold = 0
    if silver < 0:
        silver = 0
    if bronze < 0:
        bronze = 0
    all.at[county[i], 'gold'] = round(gold)
    all.at[county[i], 'silver'] = round(silver)
    all.at[county[i], 'bronze'] = round(bronze)
    all.at[county[i], 'all'] = round(gold) + round(silver) + round(bronze)'''
    all.at[county[i], 'gold'] = gold
    all.at[county[i], 'silver'] = silver
    all.at[county[i], 'bronze'] = bronze
    all.at[county[i], 'all'] = gold + silver + bronze
all.to_excel('pred_count.xlsx', index=False)