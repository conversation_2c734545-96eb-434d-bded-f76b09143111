import pandas as pd
import numpy as np
from sklearn.model_selection import GridSearchCV, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import xlsxwriter
import time
from sklearn.utils.validation import check_X_y, check_array, check_is_fitted
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader
from sklearn.multioutput import MultiOutputRegressor

def load_data(data_path = '最新normalize_data.xlsx', import_path = '引用.xlsx'):
    data = pd.read_excel(data_path)
    df = pd.read_excel(import_path)
    X_name = df['自变量']
    df_cleaned = df.dropna(subset=['因变量'])
    # 提取清理后的自变量和因变量
    y_name = df_cleaned['因变量']

    train = data[data['year']<=2016]
    test = data[data['year']==2020]
    pred = data[data['year']==2024]

    X_train = train[X_name]
    X_test = test[X_name]
    y_train = train[y_name]
    y_test = test[y_name]
    X_pred = pred[X_name]

    return X_train, X_test, y_train, y_test, X_pred

class ELMRegressor(BaseEstimator, RegressorMixin):
    def __init__(self, hidden_units=10, activation_func='sigmoid', device='cpu'):
        self.hidden_units = hidden_units
        self.activation_func = activation_func
        self.input_weights = None
        self.biases = None
        self.output_weights = None
        self.activation_func_ = None
        self.device = device  # 'cuda' for GPU, 'cpu' for CPU

    def _sigmoid(self, x):
        return torch.sigmoid(x)

    def _relu(self, x):
        return torch.relu(x)

    def _tanh(self, x):
        return torch.tanh(x)

    def _leaky_relu(self, x, alpha=0.01):
        return torch.nn.functional.leaky_relu(x, negative_slope=alpha)

    def _get_activation_function(self):
        if isinstance(self.activation_func, str):
            if self.activation_func == 'sigmoid':
                return self._sigmoid
            elif self.activation_func == 'relu':
                return self._relu
            elif self.activation_func == 'tanh':
                return self._tanh
            elif self.activation_func == 'leaky_relu':
                return self._leaky_relu
            else:
                raise ValueError(f"Unsupported activation function: {self.activation_func}")
        elif not callable(self.activation_func):
            raise TypeError("activation_func must be a callable function or a pre-defined activation function name")
        else:
             return self.activation_func
    def fit(self, X, y):
        X, y = check_X_y(X, y, accept_sparse=False, y_numeric=True)

        X = torch.tensor(X, dtype=torch.float32).to(self.device)
        y = torch.tensor(y, dtype=torch.float32).to(self.device)

        self.activation_func_ = self._get_activation_function()

        self.input_weights = torch.randn(X.shape[1], self.hidden_units, device=self.device)
        self.biases = torch.randn(self.hidden_units, device=self.device)

        H = self.activation_func_(torch.matmul(X, self.input_weights) + self.biases)

        self.output_weights = torch.linalg.pinv(H) @ y  # 使用 @ 进行矩阵乘法
        return self

    def predict(self, X):
        check_is_fitted(self, ['input_weights', 'biases', 'output_weights'])
        X = check_array(X, accept_sparse=False)
        X = torch.tensor(X, dtype=torch.float32).to(self.device)

        H = self.activation_func_(torch.matmul(X, self.input_weights) + self.biases)
        return (H @ self.output_weights).cpu().detach().numpy()

    def get_feature_importance(self, X):
        check_is_fitted(self, ['input_weights'])
        return torch.sum(torch.abs(self.input_weights), axis=1).cpu().detach().numpy()


def elm_regression(X_train, X_test, y_train, y_test, X_pred, output_path="elm_results.xlsx", use_gpu=True):
    """
    使用极限学习机 (ELM) 进行多输入多输出回归拟合.

    Args:
        X_train (pd.DataFrame): 训练集输入特征.
        X_test (pd.DataFrame): 测试集输入特征.
        y_train (pd.DataFrame): 训练集输出目标.
        y_test (pd.DataFrame): 测试集输出目标.
        X_pred (pd.DataFrame): 预测集输入特征.
        output_path (str, optional): 结果输出的Excel文件路径. 默认为 "elm_results.xlsx".
        use_gpu (bool, optional): 是否使用 GPU 加速，默认为 True。

    Returns:
        None (结果将保存在Excel文件中)
    """
    # 0. 时间记录
    start_time = time.time()
    device = 'cuda' if torch.cuda.is_available() and use_gpu else 'cpu'
    print(f"Using device: {device}")

    # 1. 数据预处理: 特征缩放
    scaler_X = StandardScaler()
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    X_pred_scaled = scaler_X.transform(X_pred)

    scaler_y = StandardScaler()
    y_train_scaled = scaler_y.fit_transform(y_train)


    # 2. 定义ELM模型
    elm = ELMRegressor(device=device)

    # 3. 定义参数网格
    param_grid = {
        'hidden_units': [50, 100, 200],
        'activation_func': ['sigmoid', 'relu', 'tanh','leaky_relu']
    }

    # 4. 定义多输出模型
    multi_output_elm = MultiOutputRegressor(elm)

    # 5. 网格搜索,寻找最优参数
    grid_search = GridSearchCV(multi_output_elm, param_grid, cv=3, scoring='neg_mean_squared_error', verbose=2, n_jobs=-1)
    grid_search.fit(X_train_scaled, y_train_scaled)
    best_multi_output_elm = grid_search.best_estimator_

    print("Best params:", grid_search.best_params_)

    # 6. 模型训练
    best_multi_output_elm.fit(X_train_scaled, y_train_scaled)


    # 7. 模型预测
    y_pred_scaled = best_multi_output_elm.predict(X_pred_scaled)
    y_pred = scaler_y.inverse_transform(y_pred_scaled)

    # 8. 模型评估
    y_test_scaled_pred = best_multi_output_elm.predict(X_test_scaled)
    y_test_pred = scaler_y.inverse_transform(y_test_scaled_pred)


    mse = mean_squared_error(y_test, y_test_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_test_pred)
    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    # 9. 特征重要性分析
    feature_importance_all = {}
    for i in range(y_train.shape[1]):
        feature_importances = best_multi_output_elm.estimators_[i].get_feature_importance(X_train_scaled)
        feature_names = X_train.columns
        feature_importance = pd.DataFrame({
            'Feature': feature_names,
            'Importance': feature_importances
        })
        feature_importance_all[f'output_{i}'] = feature_importance

    # 10. 输出结果到 Excel
    with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
         # 输出预测结果
        y_pred_df = pd.DataFrame(y_pred, columns=y_train.columns)
        y_pred_df.to_excel(writer, sheet_name='Predictions', index=False)
        # 输出评价指标
        metrics_df = pd.DataFrame({
            'Metric': ['MSE', 'RMSE', 'MAE'],
            'Value': [mse, rmse, mae]
        })
        metrics_df.to_excel(writer, sheet_name='Metrics', index=False)
        # 输出特征重要性
        for output_col, feature_importance_df in feature_importance_all.items():
            feature_importance_df.to_excel(writer, sheet_name=f"FeatureImportance_{output_col}", index=False)
    end_time = time.time()
    print(f"Time taken: {end_time - start_time:.2f} seconds")
    print(f"Results saved to {output_path}")


if __name__ == '__main__':
    X_train, X_test, y_train, y_test, X_pred = load_data('最新normalize_data.xlsx', '引用.xlsx')
    elm_regression(X_train, X_test, y_train, y_test, X_pred, use_gpu=True)