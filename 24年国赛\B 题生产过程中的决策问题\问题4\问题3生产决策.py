from itertools import product
import random
# 检测决策
Dx = list(product([0, 1], repeat=8))
# 拆解决策
Dy = list(product([0, 1], repeat=3))
# 次品率,零件1，2，3，半成品1，3，成品,零件7，8
#p = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
# 购买单价,零件1，2，3，半成品1，3，成品,零件7，8
Cp1 = 2
Cp2 = 8
Cp3 = 12
Cp7 = 8
Cp8 = 12
# 检测成本,零件1，2，3，半成品1，3，成品,零件7，8
Cd = [1, 1, 2, 8, 8, 8, 1, 2]
# 成品、半成品装配成本
Ca = 8
# 调换损失
Cl = 40
# 半成品、成品拆解费用
Cr = [6,10]
# 售价
Pr = 200


# 组装后半成品数量
def N1(j):
    return (
            (1 - Dx[j][0]) * (1 - Dx[j][1]) * (1 - Dx[j][2]) +
            (1 - (1 - Dx[j][0]) * (1 - Dx[j][1]) * (1 - Dx[j][2])) * min(2 - Dx[j][0] - p[0], 2 - Dx[j][1] - p[1], 2 - Dx[j][2] - p[2])
    )


def N2(j):
    return (
            (1 - Dx[j][6]) * (1 - Dx[j][7]) +
            (1 - (1 - Dx[j][6]) * (1 - Dx[j][7])) * min(2 - Dx[j][6] - p[6], 2 - Dx[j][7] - p[7])
    )


# 成品数量
def N3(j):
    return min((N1(j) * (1 - P1(j)) * Dx[j][3] + N1(j) * (1 - Dx[j][3])),
               (N2(j) * (1 - P2(j)) * Dx[j][4] + N2(j) * (1 - Dx[j][4])))


# 半成品1、2次品率
def P1(j):
    return 1 - (1 - (1 - Dx[j][0]) * p[0]) * (1 - (1 - Dx[j][1]) * p[1]) * (1 - (1 - Dx[j][2]) * p[2]) * (1 - p[3])


# 半成品3次品率
def P2(j):
    return 1 - (1 - (1 - Dx[j][6]) * p[6]) * (1 - (1 - Dx[j][7]) * p[7]) * (1 - p[4])


# 成品次品率
def P3(j):
    return 1 - (1 - P1(j)) * (1 - P1(j)) * (1 - P2(j)) * (1 - p[5])


# 零件检测成本  零件1，2，3，半成品1，3，成品,零件7，8
def ljjccb(j):
    return (
            2 * (N1(j) * Dx[j][0] * Cd[0] + N1(j) * Dx[j][1] * Cd[1] + N1(j) * Dx[j][2] * Cd[2]) +
            N2(j) * Dx[j][6] * Cd[6] + N2(j) * Dx[j][7] * Cd[7]
    )


# 多余零件量  j为零件角标
def L(j):
    l = max(Dx[j][0] * p[0], Dx[j][1] * p[1], Dx[j][2] * p[2], Dx[j][6] * p[6], Dx[j][7] * p[7])
    return (1 - Dx[j][j]) * l + Dx[j][j] * (l - p[j])


# 零件购买成本  零件1，2，3，半成品1，3，成品,零件7，8.....j为Dx角标，k为Dy角标
def ljgmcb(j, k):
    return (
            2*(Cp1 + Cp2 + Cp3) + Cp7 + Cp8 -
            2 * (Cp1 * L(0) + Cp2 * L(1) + Cp3 * L(2)) - (Cp7 * L(6) + Cp8 * L(7)) -
            2 * P1(j) * Dy[k][0] * Dx[j][3] * (Cp1 + Cp2 + Cp3) - P2(j) * Dy[k][1] * Dx[j][4]*(Cp7+Cp8) - P3(j) * Dy[k][2] * (
                        2*(Cp1 + Cp2 + Cp3) + Cp7 + Cp8)
    )


# 零件成本
def Sx(j, k):
    return ljjccb(j) + ljgmcb(j, k)


# 半成品装配成本  零件1，2，3，半成品1，3，成品,零件7，8
def bcpzpcb(j):
    return Ca * (N1(j) * 2 + N2(j))


# 半成品检测成本  零件1，2，3，半成品1，3，成品,零件7，8
def bcpjccb(j):
    return 2 * N1(j) * Dx[j][3] * Cd[3] + N2(j) * Dx[j][4] * Cd[4]


# 半成品拆解成本  零件1，2，3，半成品1，3，成品,零件7，8.....j为Dx角标，k为Dy角标
def bcpcjcb(j, k):
    return (
            2 * N1(j) * P1(j) * Cr[0] * Dx[j][3] * Dy[k][0] + N2(j) * P2(j) * Cr[0] * Dx[j][4] * Dy[k][1] +
            2 * Cr[0] * (N1(j) * (1 - Dx[j][3]) + N1(j) * Dx[j][3] * (1 - P1(j)) - N3(j)) + Cr[0] * (N2(j) * (1 - Dx[j][4]) + N2(j) * (1 - P2(j) * Dx[j][4]) - N3(j))
    )


# 半成品成本
def Sy(j, k):
    return bcpzpcb(j) + bcpjccb(j) + bcpcjcb(j, k)


# 成品检测成本  零件1，2，3，半成品1，3，成品,零件7，8
def cpjccb(j):
    return Cd[5] * N3(j) * Dx[j][5]


# 成品装配成本  零件1，2，3，半成品1，3，成品,零件7，8
def cpzpcb(j):
    return Ca * N3(j)


# 成品拆解成本  零件1，2，3，半成品1，3，成品,零件7，8.....j为Dx角标，k为Dy角标
def cpcjcb(j, k):
    return (3 * Cr[0] + Cr[1]) * N3(j) * P3(j) * Dy[k][2]


# 成品调换成本  零件1，2，3，半成品1，3，成品,零件7，8
def cpdhcb(j):
    return Cl * N3(j) * P3(j) * (1 - Dx[j][5])


# 成品成本
def Sz(j, k):
    return cpzpcb(j) + cpjccb(j) + cpdhcb(j) + cpcjcb(j, k)


# 总成本
def S(j, k):
    return Sx(j, k) + Sy(j, k) + Sz(j, k)


# 总收入
def W(j):
    return N3(j) * (1 - P3(j)) * Pr


# 总收益
def P(j, k):
    return W(j) - S(j, k)

#统计
def count_elements(lst):
    # 创建一个空字典来存储元素计数
    element_count = {}

    # 遍历列表中的每个元素
    for element in lst:
        # 如果元素已经在字典中，增加其计数
        if element in element_count:
            element_count[element] += 1
        # 否则，将元素添加到字典中，并设置计数为1
        else:
            element_count[element] = 1

    # 找出出现次数最多的元素
    most_frequent_element = max(element_count, key=element_count.get)

    return element_count, most_frequent_element

#蒙特卡洛

# 主函数

# 决策种类
lengthx = len(Dx)
lengthy = len(Dy)
print(lengthx,lengthy)
jc = []
num = 20000
lst = []
for n in range(num):
    if n%10==0:
        print(n)
    global p
    # 取样本n=140,95%信度,在p中，0.005=（0.024，0.100），0.1=（0.060，0.160），0.2=（0.142，0.274）
    p = [random.uniform(0.060, 0.160), random.uniform(0.060, 0.160), random.uniform(0.060, 0.160),
         random.uniform(0.060, 0.160), random.uniform(0.060, 0.160), random.uniform(0.060, 0.160),
         random.uniform(0.060, 0.160), random.uniform(0.060, 0.160)]
    last = -100
    for j in range(lengthx):
        for k in range(lengthy):
            a = P(j, k)
            if last < a:
                last = a
                jc = (Dx[j], Dy[k])
    lst.append(jc)
element_count, most_frequent_element = count_elements(lst)
print(element_count, most_frequent_element)

