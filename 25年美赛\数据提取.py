import pandas as pd

# 打开 Excel 表
example_path = 'example.xlsx'  # 替换为你的文件路径
example = pd.read_excel(example_path)
athletes_path = '导出数据athletes.xlsx'
athletes = pd.read_excel(athletes_path)
use_path = '引用.xlsx'
use = pd.read_excel(use_path)

'''# 添加新行
new_row = {'特征1': 10, '特征2': 20, '特征3': 30}  # 替换为你的数据
df = df.append(new_row, ignore_index=True)

# 修改特定单元格
df.at[0, '特征1'] = 100  # 替换为你的行索引和特征名称

# 添加新列
df['新特征'] = [1, 2, 3, 4]  # 替换为你的数据'''

country_len = len(use['NOC'])
sports_len = 39
'''
print(sports_len,use.at[1,'sports']+'_weight',example['参赛'][(example['year']==1996)&(example['NOC']=='AFG')])
example.loc[(example['year'] == 1996) & (example['NOC'] == 'AFG'), '参赛'] = '新值'
print(example['参赛'][(example['year']==1996)&(example['NOC']=='AFG')])
'''
#国家
for j in range(country_len):
    print(j)
    country_name = use.at[j, 'NOC']
    get_country = athletes[athletes['NOC'] == country_name]
    #年份
    for i in range(8):
        #print(i)
        year_name = 1996 + i * 4
        get_year = get_country[(get_country['Year'] == year_name)]

        # 成员届数
        count_dict={}
        for k in range(4):
            donation_year = get_country[(get_country['Year'] == year_name-(3-k)*4)]
            get_name = donation_year['Name']
            x_dict={}
            for item in get_name:
                #print(item)
                item = str(item)
                if item in count_dict:
                    if item in x_dict:
                        pass
                    else:
                        count_dict[item]+=1
                        x_dict[item]=1
                else:
                    count_dict[item]=1
                    x_dict[item]=1
        #项目
        for k in range(sports_len):
            sports_name = use.at[k,'sports']
            #print(sports_name,sports_len)
            sex_ratio = sports_name+'_sex_ratio'
            number = sports_name+'_number'
            get_sports = get_year[get_year['Sport']==sports_name]
            #参赛人数
            player_len = len(get_sports)
            example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), number] = player_len
            #男女比例
            manNum = len(get_sports[get_sports['Sex']=='M'])
            if player_len!=0:
                example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), sex_ratio] = manNum/player_len
            else:
                example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), sex_ratio] = 0
            #赛事出现次数
            event_dict_gold={}
            event_dict_silver = {}
            event_dict_bronze = {}
            get_sports_gold = get_sports[get_sports['Medal']=='Gold']
            get_sports_silver = get_sports[get_sports['Medal'] == 'Silver']
            get_sports_bronze = get_sports[get_sports['Medal'] == 'Bronze']

            get_event_gold = get_sports_gold['Event']
            for item in get_event_gold:
                if item in event_dict_gold:
                    event_dict_gold[item]+=1
                else:
                    event_dict_gold[item]=1
            get_event_silver = get_sports_silver['Event']
            for item in get_event_silver:
                if item in event_dict_silver:
                    event_dict_silver[item] += 1
                else:
                    event_dict_silver[item] = 1
            get_event_bronze = get_sports_bronze['Event']
            for item in get_event_bronze:
                if item in event_dict_bronze:
                    event_dict_bronze[item] += 1
                else:
                    event_dict_bronze[item] = 1

            # 奖牌贡献
            gold=[0,0,0,0,0]#第几次参赛选手总的对今年奖牌的贡献
            silver=[0,0,0,0,0]
            bronze=[0,0,0,0,0]

            for event in get_sports_gold['Event']:
                x = get_sports_gold[get_sports_gold['Event']==event]#同项目金牌同赛事样本
                y = [0,0,0,0,0]#该赛事不同经验成员分布
                for name in x['Name']:
                    name = str(name)
                    y[count_dict[name]]+=1
                for p in range(1,5):
                    gold[p]+=y[p]/len(x)/len(x)

            for event in get_sports_silver['Event']:
                x = get_sports_silver[get_sports_silver['Event']==event]#同项目金牌同赛事样本
                y = [0,0,0,0,0]#该赛事不同经验成员分布
                for name in x['Name']:
                    name = str(name)
                    y[count_dict[name]]+=1
                for p in range(1,5):
                    silver[p]+=y[p]/len(x)/len(x)

            for event in get_sports_bronze['Event']:
                x = get_sports_bronze[get_sports_bronze['Event']==event]#同项目金牌同赛事样本
                y = [0,0,0,0,0]#该赛事不同经验成员分布
                for name in x['Name']:
                    name = str(name)
                    y[count_dict[name]]+=1
                for p in range(1,5):
                    bronze[p]+=y[p]/len(x)/len(x)

            '''for name in get_sports['Name']:
                player = get_sports[get_sports['Name']==name]
                name = str(name)

                if (player['Medal'] == 'Gold').all():
                    gold[count_dict[name]]+=1/event_dict_gold[player['Event'].iloc[0]]
                elif (player['Medal'] == 'Silver').all():
                    silver[count_dict[name]] += 1 / event_dict_silver[player['Event'].iloc[0]]
                elif (player['Medal'] == 'Bronze').all():
                    bronze[count_dict[name]] += 1 / event_dict_bronze[player['Event'].iloc[0]]'''
            sports_gold_name = ['','_gold1','_gold2','_gold3','_gold4']
            sports_silver_name = ['', '_silver1', '_silver2', '_silver3', '_silver4']
            sports_bronze_name = ['', '_bronze1', '_bronze2', '_bronze3', '_bronze4']
            for p in range(1,5):
                example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), sports_name+sports_gold_name[p]] = gold[p]
                example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), sports_name+sports_silver_name[p]] = silver[p]
                example.loc[(example['year'] == year_name) & (example['NOC'] == country_name), sports_name+sports_bronze_name[p]] = bronze[p]


'''x=athletes[(athletes['Year']==1996)&(athletes['NOC']=='CHN')]
print(athletes[(athletes['Year']==1996)&(athletes['NOC']=='CHN')])
print(x[(x['Event']==1)])'''

# 保存到原文件（覆盖）
example.to_excel('example.xlsx', index=False)  # index=False 表示不保存行索引