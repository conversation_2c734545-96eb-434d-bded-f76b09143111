import numpy as np
import scipy.optimize as opt

# 设备的数据
data = np.array([[110.241, 27.204, 824, 100.767],
                 [110.780, 27.456, 727, 112.220],
                 [110.712, 27.785, 742, 188.020],
                 [110.251, 27.825, 850, 258.985],
                 [110.524, 27.617, 786, 118.443],
                 [110.467, 27.921, 678, 266.871],
                 [110.047, 27.121, 575, 163.024]])

# 信号速度
v = 340  # 假设信号速度为340米/秒

# 定义要最小化的函数
def func_to_minimize(unknowns):
    x, y, z, t = unknowns
    errors = []
    for point in data:
        dx = (point[0] - x) * 97304  # 将经度差转换为距离（近似值）
        dy = (point[1] - y) * 111263  # 将纬度差转换为距离（近似值）
        dz = point[2] - z
        dt = point[3] - t
        distance = np.sqrt(dx**2 + dy**2 + dz**2)
        error = distance - v * dt
        errors.append(error)

    return np.array(errors)

# 初始猜测
initial_guess = [np.mean(data[:, 0]), np.mean(data[:, 1]), np.mean(data[:, 2]), 1]

# 进行最小二乘拟合
result = opt.least_squares(func_to_minimize, initial_guess)

# 输出结果
print("Estimated explosion location and time:", result.x)

# 获取最终误差
final_error = np.linalg.norm(result.fun)

# 输出最终误差
print("Final error:", final_error)
