import numpy as np
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

data_before = {
    0:
        [[110.50089049577147, 110.49896136984682, 110.30124704296749, 110.69805726417412],
         [27.309234589806778, 27.949470873254867, 27.650179232019862, 27.64964961244415],
         [12376.400729173038, 11520.590804247331, 11737.140436582067, 12575.97570292655],
         [11.777058333322184, 12.994342165634075, 13.91982658429122, 16.313037094110747], 0.5276728419013044],
    1:
        [[110.50209459326419, 110.49886380126429, 110.29679078410564, 110.70019997430613],
         [27.306965145597154, 27.954077247037194, 27.6533231326296, 27.650181955714377],
         [13013.728207258711, 12451.547957169772, 11521.375160588876, 13257.096471069752],
         [11.038105743830412, 11.222186409820601, 13.194578176824104, 15.424901392637082], 0.9924191476765025],
    2:
        [[110.50142892989182, 110.49923592269258, 110.29848069780876, 110.70128952370005],
         [27.30837889542648, 27.94896610882114, 27.650698652876486, 27.650452745128064],
         [12287.703927720882, 11549.183214866567, 11547.410405839602, 13590.989056200327],
         [11.855545472213084, 13.11831292850806, 13.961429877166985, 14.973185522836342], 0.5657283118251524],
    3:
        [[110.50166637529713, 110.49911072344037, 110.30052965945043, 110.69844638155888],
         [27.30804583353504, 27.949611203434497, 27.6506868079232, 27.650253704393954],
         [12140.277357194875, 11520.726982049742, 11719.753635606712, 13364.367706166287],
         [12.004299857148236, 13.00181899275667, 13.792910584935441, 15.099264803497078], 0.4057331061706678],
    4:
        [[110.50183102126, 110.49920125461092, 110.29890567854663, 110.69998125035706],
         [27.3072630217395, 27.95154814035938, 27.650075877402866, 27.649779681955117],
         [13328.05798484067, 12526.641483815669, 11117.562805098083, 13201.345717891701],
         [10.689730362392368, 11.950552350642653, 14.406350228452983, 15.592221389806173], 0.8899971632499036],
    5:
        [[110.50083456010475, 110.49805629594475, 110.2988268843335, 110.70129447061257],
         [27.309351753541467, 27.94883065735803, 27.65015713306814, 27.65045252230377],
         [12317.598606823358, 11576.118783517997, 11596.159016697833, 13592.535940613205],
         [11.841671205173931, 13.092742723350492, 14.092575090879109, 14.971454389481437], 0.4897075424692942],
    6:
        [[110.50083944456786, 110.49903908526707, 110.29907853373568, 110.69996540164031],
         [27.309341522909556, 27.949739559939452, 27.65042678830659, 27.6497786099218],
         [12322.744096650942, 11548.333757311202, 11979.346703387777, 13196.31375347773],
         [11.836029283839885, 12.970113232026733, 13.885347993048834, 15.59815993570524], 0.4104595949485185],
    7:
        [[110.50140503442599, 110.49873891081886, 110.29850343172828, 110.70036971184685],
         [27.308428009849173, 27.94888654062571, 27.6506935344213, 27.65049413984526],
         [12264.763759235268, 11503.337419817872, 11195.608180012441, 13300.233861531773],
         [11.880740511077477, 13.200502880622663, 14.175858104005119, 15.295053076629548], 0.5688729042266414],
    8:
        [[110.50178521401922, 110.4992901301587, 110.30022161843192, 110.70095563185548],
         [27.307780976014936, 27.950882692189243, 27.648055104272427, 27.651571809490175],
         [12304.694705903607, 12546.04524191798, 10857.898260994092, 13448.303217305633],
         [11.825920261365383, 12.142212886170862, 15.160451445983623, 14.846813791081416], 0.5836111115804883],
    9: [[110.50148325817842, 110.49901295584866, 110.29872840580605, 110.69898848179943],
        [27.308267224116527, 27.949477750687073, 27.650822841283233, 27.649298981121962],
        [12339.704957897162, 11728.829148178133, 12317.473414077676, 13813.581709795417],
        [11.79826048137619, 12.949390935795313, 13.52961061954868, 14.759068622873968], 0.47770514241773065],
    10:
        [[110.5008950033204, 110.49950886157626, 110.30119696187624, 110.70054448623253],
         [27.30750798073449, 27.950754870427296, 27.650254632057326, 27.64979538827933],
         [13378.636433828999, 12070.42100197177, 12158.987818317579, 13184.378975836342],
         [10.820833188408008, 12.347384369622205, 13.628058310321986, 15.491466741029715], 0.7981160195556619],
    11:
        [[110.50019677057348, 110.4994976652784, 110.30125786029997, 110.70091089925016],
         [27.309593824676874, 27.949604477258998, 27.650162887800178, 27.650304548330215],
         [12309.747624266281, 11819.64356666563, 11643.614859530766, 13455.594985275675],
         [11.972630336175541, 12.799456313971612, 13.983094071429433, 15.154153963316679], 0.5391966808469317],
    12:
        [[110.50036051923558, 110.4990194570123, 110.30131558205463, 110.70176199037114],
         [27.309531797864743, 27.949603336005264, 27.650075284437758, 27.6498667859342],
         [12249.349361060515, 11574.40976988706, 11128.394905661777, 13472.190725924605],
         [12.008885912547067, 12.931865976772002, 14.32234744282791, 15.08923658189181], 0.39840634600437874],
    13:
        [[110.50243665082223, 110.49964882725735, 110.30137479316545, 110.69985470435154],
         [27.306799590460116, 27.949973082390905, 27.650130774010044, 27.65056936005533],
         [12932.016337866933, 11973.117570921, 12183.457755414836, 13329.352883404834],
         [11.072671807976604, 12.615323585392971, 13.64555642838016, 15.35819250957493], 0.7610438381682196],
    14:
        [[110.50249544809282, 110.50013094919129, 110.30012953613016, 110.69745251323442],
         [27.30677257682012, 27.95243834572987, 27.650998062317, 27.65326432078563],
         [12914.616798438794, 12712.35143009077, 12010.134281293325, 13815.693617870447],
         [11.082281151650438, 11.524499660878648, 13.523031283300327, 14.894167325283556], 0.9999682225367169],
    15:
        [[110.50217917520915, 110.49945414231114, 110.30120268600217, 110.69369630639916],
         [27.308268774330383, 27.949571796468167, 27.650163142202626, 27.651068899175847],
         [12006.315595646041, 11816.303341261071, 11215.178797822993, 12135.660627775962],
         [11.999674090805687, 12.799050614075284, 14.246352770278355, 17.010763053520435], 0.9138175052160341],
    16:
        [[110.49721573299503, 110.49844255691809, 110.30087308416611, 110.70441409025015],
         [27.30999345770382, 27.950805765488756, 27.65022934014015, 27.649903310845033],
         [12706.536734741534, 12225.771874273285, 12017.506531769317, 13063.151628249645],
         [12.179448363174401, 12.431223418823393, 13.738109593978328, 14.799235086411016], 0.872311766095762],
    17:
        [[110.50502675383393, 110.49805894667055, 110.30015477445926, 110.6902893538855],
         [27.30471168370253, 27.950312824249735, 27.65017327404011, 27.64950950060554],
         [14096.454030556066, 11925.465473739981, 11697.34110437363, 13467.175026801126],
         [9.292223331477588, 12.834672328914499, 13.982143543601262, 17.32586906316734], 0.8499597833340621],
    18:
        [[110.50251218696124, 110.49774762075482, 110.30059990624338, 110.69394954969631],
         [27.308221283957167, 27.948933963506278, 27.650282195182953, 27.651423864715174],
         [12039.454558091616, 11497.661953275136, 11537.561970058696, 13618.742378237648],
         [11.88891929345374, 13.269204555857357, 14.032334761185474, 16.099635270563894], 0.39932051379651884],
    19:
        [[110.50254642718374, 110.49731697445377, 110.30014867976871, 110.6939546985742],
         [27.30673106017111, 27.94966292012574, 27.650371234138667, 27.651430857231393],
         [12939.38037472687, 11606.195335347256, 11773.077741165647, 13646.473988002283],
         [11.046192051697119, 13.290330394327967, 13.871964367683283, 16.08156483042647], 0.7988467571086517],
    20:
        [[110.5005606309114, 110.5001184187889, 110.30333584713344, 110.7002579785479],
         [27.30463155024204, 27.951847890079595, 27.652995136262643, 27.650076620690967],
         [13334.641585639758, 12168.953965048238, 11476.792699259702, 13120.679132116838],
         [9.976745020571395, 12.184277968210646, 14.92751167110083, 15.649220569260592], 0.9902560099623575],
    21:
        [[110.5000543346783, 110.49825435842645, 110.30325930446901, 110.70194424060095],
         [27.307375435041955, 27.949263082416735, 27.653229776639854, 27.650403912959202],
         [12411.54266166437, 11760.153540100016, 12446.78658361699, 13522.566005982619],
         [11.255385605618875, 12.875240850118182, 14.269009523363355, 15.14048473211759], 0.4966529282284843],
    22:
        [[110.50199785267468, 110.49909176117293, 110.29964042073284, 110.69775825449386],
         [27.310356162672498, 27.94968517515248, 27.650614526136504, 27.65005413462812],
         [12663.176528131882, 11512.98546463335, 11379.654292147297, 13112.299475520962],
         [11.915575143254836, 12.9916878819962, 14.36804059444762, 15.44379660084852], 0.3819508219443528],
    23:
        [[110.5036279067483, 110.500728773265, 110.30445369603797, 110.70014162239164],
         [27.30918104056927, 27.950797212288187, 27.652939119761914, 27.650287883622646],
         [12924.21837182976, 12060.476527601244, 11468.597234350544, 13393.244303133912],
         [11.83305678152314, 12.61574292886091, 15.223487927311268, 15.19932537250591], 0.9252536023949472],
    24:
        [[110.50229004493902, 110.49959058227653, 110.2994116026293, 110.69992419150837],
         [27.30725138196938, 27.952862578616646, 27.653191786021935, 27.650682272979633],
         [12994.328289795074, 12317.895609010262, 11504.219759951447, 13891.541642547869],
         [11.14855087342511, 11.735769803296149, 13.888485313457219, 14.35951149281353], 0.9943031340733212],
    25:
        [[110.50181858283399, 110.49930238454874, 110.29943451602145, 110.69701697508502],
         [27.308633416329524, 27.949155637863722, 27.651058150744095, 27.64983914102575],
         [12372.939527942362, 11634.03250877379, 11579.666132869537, 12835.659348114024],
         [11.900037305964267, 13.017406745078612, 14.136580084235419, 15.814997399212507], 0.602932410428792],
    26:
        [[110.50373352582567, 110.49902086991982, 110.30163890629332, 110.70036108613361],
         [27.309295792145544, 27.951468881160963, 27.651876866766102, 27.650486339609145],
         [12994.243189856526, 12581.314033024164, 11343.559227684209, 13303.763911339793],
         [11.801143293294457, 11.92032607790496, 14.72787279961392, 15.291432696995976], 0.5379889213097013],
    27:
        [[110.50282762814237, 110.49940137095675, 110.29980884765394, 110.69797813367472],
         [27.307648278989582, 27.951618670111316, 27.65067097134639, 27.64763074066197],
         [13711.759065755903, 12455.693414590458, 11193.840323869223, 13766.948702090345],
         [10.557015204708117, 11.994568095362098, 14.512589862914574, 15.179716973752829], 0.9999924611299158],
    28: [[110.50152179240526, 110.49889378470293, 110.29934379446009, 110.69836819016254],
         [27.30966606646156, 27.948834606964525, 27.65068849714016, 27.649468867466414],
         [11969.491055162902, 11457.438145519605, 11692.213640312839, 13513.29252597577],
         [12.395313013860418, 13.258391125550054, 14.09484042862867, 15.091206914662713], 0.4141722978620955],
    29:
        [[110.50222913470702, 110.4990112476787, 110.29973217485096, 110.69903407185843],
         [27.307206519794995, 27.94946853709451, 27.651157271232357, 27.649286496668527],
         [12910.501986005154, 11724.984665607239, 11254.699250445616, 13835.405055734756],
         [11.21565361074624, 12.953817586110148, 14.392175622360838, 14.734659926291837], 0.7515389435972013],
    30:

        [[110.49924742958262, 110.5002757876525, 110.30041907349968, 110.70142488771643],
         [27.29338694396185, 27.952942482462195, 27.64925819408159, 27.648935632695856],
         [13138.694057970517, 13333.286749044828, 12398.326969924992, 13392.35456050891],
         [6.680025270113235, 10.802847474472681, 13.155573975881406, 14.983050123295962], 0.744786987731539],
    31:
        [[110.50117095899692, 110.4996625590704, 110.3006166909601, 110.70433018919003],
         [27.30986093450709, 27.951082890611126, 27.649511320862935, 27.646078993894534],
         [13415.933883379068, 12172.657648256056, 12337.934654952107, 14005.364259207978],
         [11.509931442442848, 12.219617153179794, 13.275600312739112, 13.406780247397792], 0.8384832663423462],
    32:
        [[110.50107089273253, 110.49923554795436, 110.30116360101238, 110.70041719781511],
         [27.310763027007177, 27.94897211322733, 27.649952222808103, 27.64853908322109],
         [12578.106147520006, 11552.748661092202, 11401.997092491527, 13505.73307098856],
         [12.262640430406732, 13.113738849465333, 14.094785111151555, 14.599677094690703], 0.47016395153837875],
    33:
        [[110.50238501029916, 110.49930675791875, 110.30039382764379, 110.70276963424405],
         [27.306972940964645, 27.94916764531804, 27.649909403981656, 27.64770949787822],
         [12924.702151724186, 11639.331585885364, 12067.463391792759, 13968.14825896981],
         [11.132402241545561, 13.011104040121321, 13.566412095918217, 13.7441781309107], 0.6338778896058661],
    34:
        [[110.50048436822462, 110.49742460194064, 110.30009884809468, 110.70799288736387],
         [27.310200283076405, 27.94962175597055, 27.650253615424123, 27.64550451922791],
         [12967.557768630726, 11572.794176793497, 11617.4166566347, 14145.268684073344],
         [12.051292120099248, 13.332763024721185, 14.067819224995503, 12.359349433614824], 0.6320367238370364]
}

data_after = []

for key, value in data_before.items():
    # print(value[1])
    for i in range(4):
        data_after.append([value[0][i], value[1][i], value[2][i], value[3][i], value[4]])

data = np.array(data_after)
# 只使用前四维数据进行聚类
data_for_clustering = data[:, [0, 1]]

kmeans = KMeans(n_clusters=4)  # 假设我们想要分成4个簇
kmeans.fit(data_for_clustering)

# 获取聚类结果
labels = kmeans.labels_
# print(labels)
# a=list(labels)
# print(a.count(0),a.count(1),a.count(2),a.count(3))

ans = []

# 创建一个列表，用于存储每个类的数据点
clusters = {i: [] for i in range(kmeans.n_clusters)}

# 将数据点分配到相应的类中
for i, label in enumerate(labels):
    clusters[label].append(list(data[i]))

for key, value in clusters.items():
    # print(key, value)
    ans.append(value)

sorted_ans = sorted(ans, key=lambda x: x[0][3])
for i in range(4):
    print("第", i + 1, "声音爆:")
    print(sorted_ans[i])

# 创建一个新的图形
fig = plt.figure()

# 添加一个三维子图
ax = fig.add_subplot(111, projection='3d')

# 设置坐标轴标签
ax.set_xlabel('经度', fontproperties='SimHei', fontsize=14)
ax.set_ylabel('纬度', fontproperties='SimHei', fontsize=14)
ax.set_zlabel('高程', fontproperties='SimHei', fontsize=14)

# 在三维坐标系中绘制第一类点
ax.scatter([point[0] for point in sorted_ans[0]],
           [point[1] for point in sorted_ans[0]],
           [point[2] for point in sorted_ans[0]],
           color='red', marker='o', label='第 1 声音爆')
# 在三维坐标系中绘制第二类点
ax.scatter([point[0] for point in sorted_ans[1]],
           [point[1] for point in sorted_ans[1]],
           [point[2] for point in sorted_ans[1]],
           color='yellow', marker='^', label='第 2 声音爆')
# 在三维坐标系中绘制第三类点
ax.scatter([point[0] for point in sorted_ans[2]],
           [point[1] for point in sorted_ans[2]],
           [point[2] for point in sorted_ans[2]],
           color='green', marker='^', label='第 3 声音爆')
# 在三维坐标系中绘制第四类点
ax.scatter([point[0] for point in sorted_ans[3]],
           [point[1] for point in sorted_ans[3]],
           [point[2] for point in sorted_ans[3]],
           color='blue', marker='^', label='第 4 声音爆')

# 添加图例
ax.legend(prop={'family': 'SimHei', 'size': 14})

# 显示图形
plt.show()


w = [0,0,0,0]
for i in range(35):
    for j in range(4):
        w[j] += sorted_ans[j][i][4]

xyzt = []

for i in range(4):
    x,y,z,t=0,0,0,0
    for j in range(35):
        x += sorted_ans[i][j][0] * sorted_ans[i][j][4] / w[i]
        y += sorted_ans[i][j][1] * sorted_ans[i][j][4] / w[i]
        z += sorted_ans[i][j][2] * sorted_ans[i][j][4] / w[i]
        t += sorted_ans[i][j][3] * sorted_ans[i][j][4] / w[i]
    xyzt.append([x,y,z,t])

print("加权平均值：",xyzt)

before = [
    [110.50000088776977,27.309998591248902,12513.948080239821,12.000041737415733],
    [110.5000007199096,27.950002809255356,11530.906578459051,12.999014823236507],
    [110.29999876256734,27.650000589585503,11477.580862619632,14.000285642948944],
    [110.69999879039993,27.65000071037475,13468.158918961706,14.99996941493694]
]

# 地理坐标转换为距离的参数
jin = 97304
wei = 111263
v = 340
for i in range(4):
    d = np.sqrt((jin * (xyzt[i][0] - before[i][0])) ** 2 + (wei * (xyzt[i][1] - data[i][1])) ** 2 + (xyzt[i][2] - data[i][2]) ** 2)
    print(d)