import pandas as pd
import numpy as np
from openpyxl import load_workbook

# 假设Excel文件名为"data.xlsx"
file1_path = '/\\煤矿预警\\问题1、1\\预处理\\smoothed_data_EMR.xlsx'  # 替换为您的 Excel 文件路径
file2_path = '/\\煤矿预警\\问题1、1\\时间区间\\Duration_EMR.xlsx'  # 替换为您的 Excel 文件路径
data_df = pd.read_excel(file1_path)
time_df = pd.read_excel(file2_path, sheet_name='C')

# 筛选出C类数据
c_data = data_df[data_df['类别 (class)'] == 'C']
result = []

# 遍历“time”工作表中的每个时间区间
for index, row in time_df.iterrows():
    start_time = pd.to_datetime(row['Start'])
    end_time = pd.to_datetime(row['End'])
    # 提取该时间区间内的C类数据
    interval_data = c_data[(c_data['时间 (time)'] >= start_time) & (c_data['时间 (time)'] <= end_time)].copy()  # 使用.copy()来避免SettingWithCopyWarning

    # 提取幅值数据
    amplitude_values = interval_data['EMR_smoothed'].values

    # 计算二阶差分
    acceleration_values = np.diff(amplitude_values, n=2)

    # 计算平均加速度
    average_acceleration = np.mean(acceleration_values) if len(acceleration_values) > 0 else np.nan

    # 将平均加速度添加到结果列表
    result.append(average_acceleration)

# 加载已有的Excel文件
wb = load_workbook('average_growth_rates.xlsx')

# 新建一个工作表，名为'Sheet2'
ws = wb.create_sheet('EMR_C')

# 将'result'列表内容写入工作表
for row, value in enumerate(result, start=1):
    ws.cell(row=row, column=1, value=value)

# 保存更改
wb.save('average_growth_rates.xlsx')
