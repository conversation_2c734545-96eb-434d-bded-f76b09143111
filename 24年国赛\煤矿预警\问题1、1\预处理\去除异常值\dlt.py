import pandas as pd

df = pd.read_excel('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\附件1 (Attachment 1).xlsx', sheet_name='AE')

def process_class_changes(df):
    current_class = None
    current_index = None
    current_time = None

    start_class = None
    start_index = None
    start_time = None

    last_class = None
    last_time = None
    last_index = None


    new_rows = []

    for index, row in df.iterrows():
        '''
        if last_time == current_time:
            continue
        '''


        last_class = current_class
        last_time = current_time
        last_index = current_index
        current_class = row['类别 (class)']
        current_time = row['时间 (time)']
        current_index = index

        #开始
        if last_class == None and current_class == 'D/E':
            start_time = current_time
            start_index = current_index
            start_class = current_class

        elif last_class != None and last_class != 'D/E' and current_class == 'D/E':
            start_time = last_time
            start_class = last_class
            start_index = last_index

        #结束
        elif last_class == 'D/E' and current_class != 'D/E' and current_class != None:
            if current_time - start_time < pd.Timedelta(hours=1):
                if start_class==current_class:
                    for i in range(start_index, index):
                        #new_row = df.iloc[i].copy()
                        #new_row['类别 (class)'] = start_class
                        #new_rows.append(new_row)
                        df.at[i, '类别 (class)'] = start_class
                    start_index = None
                    start_time = None
                    start_class = None

    current_class = None
    current_index = None
    current_time = None
    last_class = None
    last_time = None
    last_index = None

    for index, row in df.iterrows():

        last_class = current_class
        last_time = current_time
        last_index = current_index
        current_class = row['类别 (class)']
        current_time = row['时间 (time)']
        current_index = index

        if last_time != current_time :
            new_row = df.iloc[index].copy()
            new_rows.append(new_row)


    return pd.DataFrame(new_rows).reset_index(drop=True)

new_df = process_class_changes(df)
new_df.to_excel('New_AE.xlsx', index=False)
