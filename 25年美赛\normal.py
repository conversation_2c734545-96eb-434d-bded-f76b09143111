import pandas as pd
import numpy as np

def load_data():
    # 加载数据
    data = pd.read_excel(r'C:\Users\<USER>\Desktop\all_data.xlsx')
    df = pd.read_excel(r'C:\Users\<USER>\Desktop\引用.xlsx')
    
    # 获取自变量和因变量的列名
    X_name = df['自变量'].dropna().tolist()
    y_name = df['因变量'].dropna().tolist()

    return data, X_name, y_name

def normalize_data(data, X_name):
    # 按年份分组
    grouped = data.groupby('year')
    
    # 对每个年份的数据进行归一化
    normalized_data = []
    for year, group in grouped:
        # 选取自变量列
        X = group[X_name]
        
        # 最大最小值归一化
        X_norm = (X - X.min()) / (X.max() - X.min())
        
        # 处理 _weight 变量
        weight_cols = [col for col in X_name if '_weight' in col]
        if weight_cols:
            weight_sum = X_norm[weight_cols].sum(axis=1)
            X_norm[weight_cols] = X_norm[weight_cols].div(weight_sum, axis=0)
        
        # 保留其他列
        group[X_name] = X_norm
        normalized_data.append(group)
    
    # 合并所有年份的数据
    normalized_data = pd.concat(normalized_data, ignore_index=True)
    return normalized_data

def save_normalized_data(normalized_data):
    # 保存归一化后的数据
    normalized_data.to_excel(r'C:\Users\<USER>\Desktop\new.xlsx', index=False)

if __name__ == "__main__":
    data, X_name, y_name = load_data()
    normalized_data = normalize_data(data, X_name)
    save_normalized_data(normalized_data)
    print("ok了new.xlsx")
