import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 假设我们有两类点的数据
# 第一类点
points_class1 = [(110.241,27.204,824),
                 (110.783,27.456,727),
                 #(110.762,27.785,742),
                 (110.251,28.025,850),
                 #(110.524,27.617,786),
                 #(110.467,28.081,678),
                 (110.047,27.521,575)]  # 用实际坐标替换
# 第二类点
points_class2 = [(110.50000078281228, 27.309998602863633, 12513.944788484045),
                 (110.50001815390614, 27.950046417137713, 11549.434783817196),
                 (110.29999876307822, 27.65000057760299,  11477.589663889194),
                 (110.69999887532869, 27.650000439203225, 13468.154529633885)]
# 用实际坐标替换

# 创建一个新的图形
fig = plt.figure()

# 添加一个三维子图
ax = fig.add_subplot(111, projection='3d')

# 设置坐标轴标签
ax.set_xlabel('经度', fontproperties='SimHei', fontsize=14)
ax.set_ylabel('纬度', fontproperties='SimHei', fontsize=14)
ax.set_zlabel('高程', fontproperties='SimHei', fontsize=14)

# 在三维坐标系中绘制第一类点
ax.scatter([point[0] for point in points_class1],
           [point[1] for point in points_class1],
           [point[2] for point in points_class1],
           color='red', marker='o', label='设备')

# 在三维坐标系中绘制第二类点
ax.scatter([point[0] for point in points_class2],
           [point[1] for point in points_class2],
           [point[2] for point in points_class2],
           color='blue', marker='^', label='音爆发生地点')

# 添加图例
ax.legend(prop={'family' : 'SimHei', 'size' : 14})

# 显示图形
plt.show()
