import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 假设Excel文件名为'sound_data.xlsx'，并且文件位于与Python脚本相同的目录中
file_path = r'D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\预处理\去除异常值\New_EMR.xlsx'

# 读取Excel文件
#df = pd.read_excel(file_path, sheet_name='AE')
df = pd.read_excel(file_path)

# 将时间列转换为datetime类型
df['时间 (time)'] = pd.to_datetime(df['时间 (time)'])

# 对'电磁辐射 (EMR)'列进行移动平均平滑处理
# 这里以5个数据点的移动平均为例，你可以根据需要调整窗口大小
df['EMR_smoothed'] = df['电磁辐射 (EMR)'].rolling(window=5).mean()

# 按类别分组，并可视化每个类别的原始和平滑后的'电磁辐射 (EMR)'数据
unique_classes = df['类别 (class)'].unique()
plt.figure(figsize=(15, 10))
for i, label in enumerate(unique_classes):
    subset = df[df['类别 (class)'] == label]
    plt.subplot(2, 2, i+1)
    plt.plot(subset['时间 (time)'], subset['电磁辐射 (EMR)'], label='原始数据')
    plt.plot(subset['时间 (time)'], subset['EMR_smoothed'], label='平滑后的数据')
    plt.title(f'class {label}')
    plt.xlabel('time')
    plt.ylabel('EMR')
    plt.legend()

plt.tight_layout()
plt.show()

# 保存处理后的数据到新的Excel文件
df.to_excel('smoothed_data_EMR.xlsx', index=False)
