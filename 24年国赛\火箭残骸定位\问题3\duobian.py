import numpy as np
import scipy.optimize as opt

# 更新数据格式以包含所有设备和音爆时间
data = {
    'A': {'longitude': 110.241, 'latitude': 27.204, 'elevation': 824, 'times': [100.767, 164.229, 214.850, 270.065]},
    'B': {'longitude': 110.783, 'latitude': 27.456, 'elevation': 727, 'times': [92.453, 112.220, 169.362, 196.583]},
    'C': {'longitude': 110.762, 'latitude': 27.785, 'elevation': 742, 'times': [75.560, 110.696, 156.936, 188.020]},
    'D': {'longitude': 110.251, 'latitude': 28.025, 'elevation': 850, 'times': [94.653, 141.409, 196.517, 258.985]}
}

# 信号速度
v = 340  # 假设信号速度为340米/秒

# 定义要最小化的函数
def func_to_minimize(unknowns, device_data):
    x, y, z, t = unknowns
    errors = []
    for device in device_data:
        for i in range(4):  # 假设有四个音爆事件
            dx = (device_data[device]['longitude'] - x) * 97304  # 将经度差转换为距离（近似值）
            dy = (device_data[device]['latitude'] - y) * 111263  # 将纬度差转换为距离（近似值）
            dz = device_data[device]['elevation'] - z
            dt = device_data[device]['times'][i] - t
            distance = np.sqrt(dx**2 + dy**2 + dz**2)
            error = distance - v * dt
            errors.append(error)
    return np.array(errors)

# 对于每个音爆事件，估计其位置和时间
explosion_events = []
for i in range(4):
    initial_guess = [np.mean([data[device]['longitude'] for device in data]),
                     np.mean([data[device]['latitude'] for device in data]),
                     np.mean([data[device]['elevation'] for device in data]),
                     np.mean([data[device]['times'][i] for device in data])]

    # 进行最小二乘拟合
    result = opt.least_squares(lambda u: func_to_minimize(u, data), initial_guess)

    # 保存结果
    explosion_events.append(result.x)

# 输出结果
for i, event in enumerate(explosion_events):
    print(f"Estimated explosion {i+1} location and time:", event)

# 计算每个设备的最终误差
final_errors = {}
for device in data:
    errors = []
    for i, event in enumerate(explosion_events):
        dx = (data[device]['longitude'] - event[0]) * 97304
        dy = (data[device]['latitude'] - event[1]) * 111263
        dz = data[device]['elevation'] - event[2]
        dt = data[device]['times'][i] - event[3]
        distance = np.sqrt(dx**2 + dy**2 + dz**2)
        error = distance - v * dt
        errors.append(error)
    final_errors[device] = np.linalg.norm(np.array(errors))

# 输出每个设备的最终误差
for device in final_errors:
    print(f"Final error for device {device}:", final_errors[device])
