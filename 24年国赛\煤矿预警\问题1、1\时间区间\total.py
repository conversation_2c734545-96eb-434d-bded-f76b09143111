import pandas as pd

# 加载Excel文件
xlsx = pd.ExcelFile('D:\pycharm\pycharmProjects\数学建模培训\煤矿预警\问题1、1\Duration_EMRadd.xlsx')  # 替换为您的Excel文件名

# 读取所有工作表并添加类别列
dfs = {}
for sheet in ['A', 'B', 'C']:
    df = xlsx.parse(sheet)
    # 添加类别列，特别处理'D_E'工作表
    df['Category'] = 'D/E' if sheet == 'D_E' else sheet
    dfs[sheet] = df

# 将所有数据合并到一个DataFrame中
combined_df = pd.concat(dfs.values(), ignore_index=True)

# 将'Start'和'End'列转换为datetime类型
combined_df['Start'] = pd.to_datetime(combined_df['Start'])
combined_df['End'] = pd.to_datetime(combined_df['End'])

# 按时间顺序排序
combined_df = combined_df.sort_values(by='Start')

# 保存到新的Excel文件
combined_df.to_excel('totalEMR.xlsx', index=False)
