import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.model_selection import GridSearchCV
from sklearn.multioutput import MultiOutputRegressor
from sklearn.preprocessing import StandardScaler
import os

def load_data(data_path='normalize_data.xlsx', import_path='引用.xlsx'):
    data = pd.read_excel(data_path)
    df = pd.read_excel(import_path)

    # 确保'自变量'和'因变量'在列中存在
    X_name = df['自变量'].tolist()
    y_name = df['因变量'].dropna().tolist()  # 过滤掉 NaN 值
    print("y_name 的值:", y_name)
    df_cleaned = df.dropna(subset=['因变量'])

    # 选择训练集、测试集、预测集
    train = data[data['year'] <= 2016]
    test = data[data['year'] == 2020]
    pred = data[data['year'] == 2024]

    X_train = train[X_name]
    X_test = test[X_name]
    y_train = train[y_name]
    y_test = test[y_name]
    X_pred = pred[X_name]

    print(f"数据加载完毕，训练集大小: {X_train.shape}, 测试集大小: {X_test.shape}")
    return X_train, X_test, y_train, y_test, X_pred


def xgboost_regression(X_train, X_test, y_train, y_test, X_pred, output_path="xgboost_results.xlsx"):
    """
    使用 XGBoost 进行多输入多输出回归拟合，并保存结果到 Excel。

    Args:
        X_train (pd.DataFrame): 训练集输入特征.
        X_test (pd.DataFrame): 测试集输入特征.
        y_train (pd.DataFrame): 训练集输出目标.
        y_test (pd.DataFrame): 测试集输出目标.
        X_pred (pd.DataFrame): 预测集输入特征.
        output_path (str, optional): 结果输出的 Excel 文件路径. 默认为 "xgboost_results.xlsx".
    """
    # 特征标准化
    scaler_X = StandardScaler()
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    X_pred_scaled = scaler_X.transform(X_pred)

    scaler_y = StandardScaler()
    y_train_scaled = scaler_y.fit_transform(y_train)

    # 初始化 XGBoost 模型
    xgb_model = xgb.XGBRegressor(
        objective='reg:squarederror',
        eval_metric='rmse',
        tree_method='hist',  # 使用新的推荐设置
        device='cuda',  # 使用 GPU 加速
        random_state=42
    )

    # 包装成多输出模型
    multi_output_model = MultiOutputRegressor(xgb_model)

    # 参数网格
    param_grid = {
        "estimator__n_estimators": [100, 200],
        "estimator__max_depth": [6, 8],
        "estimator__learning_rate": [0.01, 0.1],
        "estimator__subsample": [0.8, 1],
        "estimator__colsample_bytree": [0.8, 1],
    }

    # 使用 GridSearchCV 进行超参数调优
    print("开始网格搜索...")
    grid_search = GridSearchCV(
        multi_output_model,
        param_grid,
        cv=3,
        scoring="neg_mean_squared_error",
        verbose=2,
        n_jobs=-1
    )
    grid_search.fit(X_train_scaled, y_train_scaled)
    best_model = grid_search.best_estimator_

    # 打印最佳参数
    print("最佳参数:", grid_search.best_params_)

    # 使用最佳模型进行预测
    y_pred_scaled = best_model.predict(X_pred_scaled)
    y_pred = scaler_y.inverse_transform(y_pred_scaled)

    # 测试集上的预测
    y_test_pred_scaled = best_model.predict(X_test_scaled)
    y_test_pred = scaler_y.inverse_transform(y_test_pred_scaled)

    # 计算性能指标
    mse = mean_squared_error(y_test, y_test_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_test_pred)

    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")

    # 获取特征重要性
    feature_importances = np.mean(
        [est.feature_importances_ for est in best_model.estimators_], axis=0
    )
    feature_importance_df = pd.DataFrame({
        "Feature": X_train.columns,
        "Importance": feature_importances
    }).sort_values(by="Importance", ascending=False)

    # 保存结果到 Excel
    with pd.ExcelWriter(output_path, engine="xlsxwriter") as writer:
        # 保存预测结果
        y_pred_df = pd.DataFrame(y_pred, columns=y_train.columns)
        y_pred_df.to_excel(writer, sheet_name="Predictions", index=False)

        # 保存性能指标
        metrics_df = pd.DataFrame({
            "Metric": ["MSE", "RMSE", "MAE"],
            "Value": [mse, rmse, mae]
        })
        metrics_df.to_excel(writer, sheet_name="Performance Metrics", index=False)

        # 保存特征重要性
        feature_importance_df.to_excel(writer, sheet_name="Feature Importance", index=False)

    print(f"结果已保存到 {os.path.abspath(output_path)}")


# 主函数
if __name__ == "__main__":
    # 示例：假设 X_train, X_test, y_train, y_test, X_pred 已经事先准备好
    # 替换为你的实际数据加载代码
    X_train, X_test, y_train, y_test, X_pred = load_data('normalize_data.xlsx', '引用.xlsx')
    xgboost_regression(X_train, X_test, y_train, y_test, X_pred)
